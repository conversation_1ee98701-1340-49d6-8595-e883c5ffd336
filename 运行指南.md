# Spark数据处理实训运行指南

## 环境准备

### 1. 检查Java环境
```bash
java -version
```
如果没有安装Java，请安装：
```bash
sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel
```

### 2. 检查Scala环境
```bash
scala -version
```
如果没有安装Scala，请下载安装：
```bash
# 下载Scala
wget https://downloads.lightbend.com/scala/2.12.15/scala-2.12.15.tgz
tar -xzf scala-2.12.15.tgz
sudo mv scala-2.12.15 /opt/scala
echo 'export PATH=/opt/scala/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 3. 检查Spark环境
```bash
spark-submit --version
```
如果没有安装Spark，请下载安装：
```bash
# 下载Spark
wget https://archive.apache.org/dist/spark/spark-3.2.0/spark-3.2.0-bin-hadoop3.2.tgz
tar -xzf spark-3.2.0-bin-hadoop3.2.tgz
sudo mv spark-3.2.0-bin-hadoop3.2 /opt/spark
echo 'export SPARK_HOME=/opt/spark' >> ~/.bashrc
echo 'export PATH=$SPARK_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

## 运行方式一：使用spark-shell（推荐）

### 1. 启动spark-shell
```bash
cd /home/<USER>/me
spark-shell
```

### 2. 在spark-shell中运行
复制粘贴 `spark_commands.scala` 文件中的代码，分段执行：

```scala
// 首先执行人口年龄分析部分
// 复制粘贴第一部分代码...

// 然后执行咖啡数据分析部分  
// 复制粘贴第二部分代码...
```

### 3. 查看结果
```scala
// 查看人口年龄分析结果
sc.textFile("people_age_analysis/part-00000").collect().foreach(println)

// 查看咖啡分析结果
sc.textFile("coffee_analysis_results/part-00000").collect().foreach(println)
```

## 运行方式二：编译运行Scala程序

### 1. 生成人口数据
```bash
scala GeneratePeopleAge.scala
```

### 2. 计算平均年龄
```bash
# 编译Scala文件
scalac -cp "$SPARK_HOME/jars/*" CalculateAverageAge.scala

# 运行Spark程序
spark-submit --class CalculateAverageAge --master local[*] .
```

### 3. 分析咖啡数据
```bash
# 编译Scala文件
scalac -cp "$SPARK_HOME/jars/*" CoffeeChainAnalysis.scala

# 运行Spark程序
spark-submit --class CoffeeChainAnalysis --master local[*] .
```

## 运行方式三：使用运行脚本

### 1. 给脚本执行权限
```bash
chmod +x run_analysis.sh
```

### 2. 运行脚本
```bash
./run_analysis.sh
```

## 查看结果

### 1. 查看生成的文件
```bash
ls -la
```

### 2. 查看人口年龄数据
```bash
head -10 /home/<USER>/me/peopleage.txt
```

### 3. 查看分析结果
```bash
# 人口年龄分析结果
cat /home/<USER>/me/people_age_analysis/part-00000

# 或者（如果使用方式二）
cat /home/<USER>/me/average_age_result.txt/part-00000

# 咖啡分析结果
cat /home/<USER>/me/coffee_analysis_results/part-00000

# 或者（如果使用方式二）
cat /home/<USER>/me/coffee_sales_ranking/part-00000
cat /home/<USER>/me/coffee_distribution_analysis/part-00000
```

## 常见问题解决

### 1. 内存不足错误
```bash
# 增加Spark driver内存
spark-shell --driver-memory 2g --executor-memory 2g
```

### 2. 找不到文件错误
确保CoffeeChain.csv文件在正确目录：
```bash
ls -la /home/<USER>/me/CoffeeChain.csv
```

### 3. 权限问题
```bash
# 给当前用户读写权限
chmod 755 /home/<USER>/me
chmod 644 /home/<USER>/me/*.csv /home/<USER>/me/*.scala
```

### 4. Java版本问题
确保使用Java 8或Java 11：
```bash
sudo alternatives --config java
```

## 截图建议

为了完成实训报告，建议截取以下画面：

1. **环境检查**：
   - `java -version` 输出
   - `scala -version` 输出  
   - `spark-submit --version` 输出

2. **数据生成**：
   - 运行 `scala GeneratePeopleAge.scala` 的输出
   - `head -10 /home/<USER>/me/peopleage.txt` 的结果

3. **Spark运行**：
   - spark-shell启动画面
   - 人口年龄分析的输出结果
   - 咖啡数据分析的输出结果

4. **结果文件**：
   - `ls -la` 显示生成的文件和目录
   - 各个结果文件的内容

## 注意事项

1. **文件路径**：确保所有文件都在 `/home/<USER>/me/` 目录下
2. **数据文件**：确保 `/home/<USER>/me/CoffeeChain.csv` 文件完整且格式正确
3. **内存设置**：根据虚拟机配置调整Spark内存参数
4. **网络连接**：如果需要下载依赖，确保网络连接正常
5. **权限设置**：确保用户有读写 `/home/<USER>/me/` 目录的权限

## 实训报告填写

运行完成后，请：

1. 将运行过程的截图插入实训报告对应位置
2. 将实际的运行结果替换报告中的示例结果
3. 根据实际运行情况调整分析内容
4. 填写个人信息（姓名、学号等）

祝您实训顺利！
