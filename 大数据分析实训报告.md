# 基于Spark的大数据处理与分析实训报告

**姓名：** [请填写您的姓名]  
**学号：** [请填写您的学号]  
**班级：** [请填写您的班级]  
**实训时间：** [请填写实训时间]  

---

## 一、实训背景与目标

### 1.1 实训背景

随着大数据时代的到来，海量数据的处理和分析已成为各行各业的核心需求。Apache Spark作为当前最流行的大数据处理框架，具有高性能、易用性和通用性等优势，被广泛应用于数据科学、机器学习和实时分析等领域。

本次实训通过两个实际的数据分析项目，深入学习Spark RDD编程技术，掌握大数据处理的核心方法和实践技能。

### 1.2 实训目标

1. **技术目标**：
   - 掌握Apache Spark的基本概念和架构
   - 熟练使用Spark RDD进行数据处理和分析
   - 学习Scala编程语言在大数据处理中的应用
   - 掌握数据清洗、转换和聚合等核心技术

2. **应用目标**：
   - 完成人口年龄数据的统计分析
   - 实现咖啡连锁店销售数据的多维度分析
   - 培养从数据中发现业务洞察的能力

3. **能力目标**：
   - 提升大数据思维和分析能力
   - 增强解决实际业务问题的技能
   - 培养数据驱动决策的意识

## 二、实验环境与工具

### 2.1 硬件环境
- **操作系统**：CentOS Linux（虚拟机环境）
- **内存配置**：建议4GB以上
- **存储空间**：至少10GB可用空间
- **工作目录**：`/home/<USER>

### 2.2 软件环境
- **Java环境**：OpenJDK 8 或 Oracle JDK 8/11
- **Scala版本**：Scala 2.12.x
- **Spark版本**：Apache Spark 3.x
- **开发工具**：Spark Shell、文本编辑器
- **数据文件**：CoffeeChain.csv（咖啡连锁店销售数据）

### 2.3 环境配置验证
```bash
# 验证Java环境
java -version

# 验证Scala环境
scala -version

# 验证Spark环境
spark-submit --version
spark-shell --version
```

## 三、实验内容与实现

### 3.1 第一部分：人口年龄数据分析

#### 3.1.1 数据生成模块

**功能描述**：生成模拟的人口年龄数据，包含ID、姓名、年龄、性别、城市等字段。

**核心代码实现**：

```scala
// DataGenerator.scala - 人口数据生成器
object DataGenerator {
  val firstNames = Array("张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴")
  val secondNames = Array("伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军")
  val cities = Array("北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都")
  val genders = Array("男", "女")
  
  def main(args: Array[String]): Unit = {
    val recordCount = 1500  // 生成1500条记录
    val minAge = 16         // 最小年龄
    val maxAge = 85         // 最大年龄
    val outputPath = "/home/<USER>/population_data.txt"
    
    val random = new Random()
    val writer = new PrintWriter(new File(outputPath))
    
    try {
      writer.println("ID,姓名,年龄,性别,城市")
      
      for (i <- 1 to recordCount) {
        val id = f"P$i%06d"
        val name = firstNames(random.nextInt(firstNames.length)) + 
                  secondNames(random.nextInt(secondNames.length))
        val age = minAge + random.nextInt(maxAge - minAge + 1)
        val gender = genders(random.nextInt(genders.length))
        val city = cities(random.nextInt(cities.length))
        
        writer.println(s"$id,$name,$age,$gender,$city")
      }
    } finally {
      writer.close()
    }
  }
}
```

**数据格式示例**：
```
ID,姓名,年龄,性别,城市
P000001,张伟,45,男,北京
P000002,李芳,32,女,上海
P000003,王娜,28,女,广州
```

#### 3.1.2 人口年龄统计分析

**功能描述**：使用Spark RDD对人口数据进行多维度统计分析。

**核心代码实现**：

```scala
// PopulationAnalyzer.scala - 人口数据分析器
object PopulationAnalyzer {
  case class PersonData(id: String, name: String, age: Int, gender: String, city: String)
  
  def main(args: Array[String]): Unit = {
    val conf = new SparkConf()
      .setAppName("Population Age Analyzer")
      .setMaster("local[*]")
    
    val sc = new SparkContext(conf)
    
    try {
      // 读取数据
      val rawData = sc.textFile("/home/<USER>/population_data.txt")
      val header = rawData.first()
      val dataLines = rawData.filter(_ != header)
      
      // 解析数据
      val personRDD = dataLines.map(parsePerson).filter(_.isDefined).map(_.get)
      personRDD.cache()
      
      // 基础年龄统计
      performBasicAgeAnalysis(personRDD)
      
      // 性别分布分析
      performGenderAnalysis(personRDD)
      
      // 城市分布分析
      performCityAnalysis(personRDD)
      
      // 年龄段分布分析
      performAgeGroupAnalysis(personRDD)
      
    } finally {
      sc.stop()
    }
  }
  
  def performBasicAgeAnalysis(personRDD: RDD[PersonData]): Unit = {
    val ages = personRDD.map(_.age)
    val totalAge = ages.reduce(_ + _)
    val count = ages.count()
    val averageAge = totalAge.toDouble / count
    val maxAge = ages.max()
    val minAge = ages.min()
    
    println(f"总人数：$count")
    println(f"平均年龄：$averageAge%.2f 岁")
    println(f"最大年龄：$maxAge 岁")
    println(f"最小年龄：$minAge 岁")
  }
}
```

**关键RDD操作说明**：

1. **数据读取**：`sc.textFile()` - 从文件创建RDD
2. **数据过滤**：`filter()` - 过滤表头和无效数据
3. **数据转换**：`map()` - 解析CSV数据为对象
4. **数据聚合**：`reduce()` - 计算年龄总和
5. **统计操作**：`count()`, `max()`, `min()` - 基础统计
6. **分组操作**：`groupByKey()` - 按性别、城市分组
7. **缓存优化**：`cache()` - 缓存频繁使用的RDD

### 3.2 第二部分：咖啡连锁店数据分析

#### 3.2.1 数据预处理

**功能描述**：读取和清洗咖啡连锁店销售数据，处理数据质量问题。

**数据结构定义**：
```scala
case class CoffeeSalesData(
  areaCode: String,      // 区域代码
  date: String,          // 日期
  market: String,        // 市场（East, West, Central, South）
  marketSize: String,    // 市场规模（Major Market, Small Market）
  product: String,       // 产品名称
  productType: String,   // 产品类型（Coffee, Tea, Espresso, Herbal Tea）
  state: String,         // 州名
  coffeeType: String,    // 咖啡类型（Regular, Decaf）
  coffeeSales: Double,   // 咖啡销售额
  profit: Double,        // 利润
  margin: Double,        // 利润率
  cogs: Double,          // 商品成本
  marketing: Double,     // 营销费用
  totalExpenses: Double  // 总费用
)
```

**数据清洗代码**：
```scala
def parseCoffeeData(line: String): Option[CoffeeSalesData] = {
  try {
    val parts = line.split(",")
    if (parts.length >= 20) {
      Some(CoffeeSalesData(
        areaCode = parts(0),
        date = parts(1),
        market = parts(2),
        // ... 其他字段解析
        coffeeSales = parseDouble(parts(12)),
        profit = parseDouble(parts(18)),
        // ... 
      ))
    } else None
  } catch {
    case _: Exception => None
  }
}

def parseDouble(str: String): Double = {
  try {
    str.replaceAll("\"", "").replaceAll(",", "").toDouble
  } catch {
    case _: Exception => 0.0
  }
}
```

#### 3.2.2 销售排名分析

**分析维度**：
1. 产品销售额排名
2. 各州销售业绩排名
3. 咖啡类型销售对比
4. 市场区域表现分析

**核心实现**：
```scala
// 产品销售排名
val productSalesRanking = coffeeDataRDD
  .map(data => (data.product, data.coffeeSales))
  .reduceByKey(_ + _)
  .sortBy(_._2, ascending = false)
  .take(15)

// 各州销售分析
val stateSalesRanking = coffeeDataRDD
  .map(data => (data.state, data.coffeeSales))
  .reduceByKey(_ + _)
  .sortBy(_._2, ascending = false)
  .collect()
```

#### 3.2.3 多维度销售分布分析

**分析内容**：

1. **市场规模分析**：
   - Major Market vs Small Market的销售表现
   - 不同市场规模的盈利能力对比

2. **地理分布分析**：
   - 各州的销售额、利润率、营销效率
   - 区域代码的业绩排名

3. **产品性能分析**：
   - 产品类型的销售表现和库存周转
   - 咖啡类型的成本效益分析

4. **时间序列分析**：
   - 月度销售趋势
   - 季节性销售模式

**关键代码示例**：
```scala
// 市场规模分析
val marketSizeAnalysis = coffeeDataRDD
  .map(data => (data.marketSize, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (marketSize, (totalSales, totalProfit, count)) =>
    val avgSales = totalSales / count
    val avgProfit = totalProfit / count
    val profitMargin = if (totalSales > 0) totalProfit / totalSales * 100 else 0
    (marketSize, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count)
  }

// 盈利能力分析
val totalSales = coffeeDataRDD.map(_.coffeeSales).sum()
val totalProfit = coffeeDataRDD.map(_.profit).sum()
val overallProfitMargin = (totalProfit / totalSales * 100).formatted("%.2f")
```

#### 3.2.4 业务洞察分析

**高级分析功能**：

1. **高价值交易分析**：识别销售额超过阈值的交易
2. **成本效益分析**：计算各产品的投资回报率
3. **库存周转分析**：评估产品的库存管理效率
4. **营销效率分析**：分析营销投入与销售产出的关系

```scala
// 高价值交易分析
val highValueTransactions = coffeeDataRDD
  .filter(_.coffeeSales > 100)
  .map(data => (data.state, data.product, data.coffeeSales, data.profit))
  .collect()
  .sortBy(_._3)(Ordering[Double].reverse)
  .take(10)

// 成本效益分析
val costEfficiencyAnalysis = coffeeDataRDD
  .map { data =>
    val efficiency = if (data.totalExpenses > 0) data.profit / data.totalExpenses else 0
    val salesEfficiency = if (data.marketing > 0) data.coffeeSales / data.marketing else 0
    (data.product, efficiency, salesEfficiency, data.profit, data.coffeeSales)
  }
  .groupBy(_._1)
  .map { case (product, records) =>
    val recordsList = records.toList
    val avgEfficiency = recordsList.map(_._2).sum / recordsList.length
    val avgSalesEfficiency = recordsList.map(_._3).sum / recordsList.length
    (product, avgEfficiency, avgSalesEfficiency)
  }
```

## 四、运行方式与操作指南

### 4.1 环境准备

1. **检查Java环境**：
```bash
java -version
# 应显示Java 8或Java 11版本信息
```

2. **检查Scala环境**：
```bash
scala -version
# 应显示Scala 2.12.x版本信息
```

3. **检查Spark环境**：
```bash
spark-submit --version
# 应显示Spark版本信息
```

### 4.2 数据准备

将咖啡数据文件放置到指定目录：
```bash
# 确保数据文件在正确位置
ls -la /home/<USER>/CoffeeChain.csv
```

### 4.3 运行方式

#### 方式一：使用自动化脚本（推荐）

```bash
# 给脚本执行权限
chmod +x /home/<USER>/ExecuteAnalysis.sh

# 运行脚本
./ExecuteAnalysis.sh
```

脚本提供以下选项：
1. 完整分析（人口数据 + 咖啡数据）
2. 仅人口数据分析
3. 仅咖啡数据分析
4. 启动Spark Shell（交互式分析）
5. 查看现有结果

#### 方式二：使用Spark Shell（交互式）

```bash
# 启动Spark Shell
cd /home/<USER>
spark-shell --driver-memory 2g --executor-memory 2g

# 在Spark Shell中复制粘贴SparkShellCommands.scala中的代码
```

#### 方式三：编译运行（手动）

```bash
# 生成人口数据
scala /home/<USER>/DataGenerator.scala

# 分析人口数据
spark-submit --class PopulationAnalyzer --master local[*] /home/<USER>/PopulationAnalyzer.scala

# 分析咖啡数据
spark-submit --class CoffeeDataAnalyzer --master local[*] /home/<USER>/CoffeeDataAnalyzer.scala
```

### 4.4 结果查看

```bash
# 查看人口分析结果
cat /home/<USER>/population_analysis_result/part-00000

# 查看咖啡分析结果
cat /home/<USER>/coffee_comprehensive_analysis/part-00000

# 在Spark Shell中查看结果
scala> sc.textFile("/home/<USER>/population_analysis_result/part-00000").collect().foreach(println)
```
