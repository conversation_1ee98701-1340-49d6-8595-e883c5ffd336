#!/bin/bash

# 咖啡数据分析运行脚本
echo "开始咖啡数据分析..."

# 检查数据文件
if [ ! -f "CoffeeChain.csv" ]; then
    echo "✗ CoffeeChain.csv 文件不存在，请确保数据文件在当前目录"
    exit 1
fi

# 使用spark-shell运行分析
echo "启动Spark咖啡数据分析..."
spark-shell --driver-memory 2g --executor-memory 2g << 'EOF'

// 定义咖啡销售数据模型
case class CoffeeSalesData(
  areaCode: String,
  date: String,
  market: String,
  marketSize: String,
  product: String,
  productType: String,
  state: String,
  coffeeType: String,
  coffeeSales: Double,
  profit: Double,
  margin: Double,
  cogs: Double,
  marketing: Double,
  totalExpenses: Double,
  inventory: Double
)

// 安全的数值解析函数
def parseDouble(str: String): Double = {
  try {
    str.replaceAll("\"", "").replaceAll(",", "").trim.toDouble
  } catch {
    case _: Exception => 0.0
  }
}

// 解析CSV数据
def parseCoffeeData(line: String): Option[CoffeeSalesData] = {
  try {
    val parts = line.split(",")
    if (parts.length >= 20) {
      Some(CoffeeSalesData(
        areaCode = parts(0),
        date = parts(1),
        market = parts(2),
        marketSize = parts(3),
        product = parts(4),
        productType = parts(5),
        state = parts(6),
        coffeeType = parts(7),
        coffeeSales = parseDouble(parts(12)),
        profit = parseDouble(parts(18)),
        margin = parseDouble(parts(15)),
        cogs = parseDouble(parts(13)),
        marketing = parseDouble(parts(16)),
        totalExpenses = parseDouble(parts(19)),
        inventory = parseDouble(parts(14))
      ))
    } else None
  } catch {
    case _: Exception => None
  }
}

// 读取咖啡数据
val coffeeRawData = sc.textFile("/home/<USER>/spark/CoffeeChain.csv")
val coffeeHeader = coffeeRawData.first()
val coffeeDataLines = coffeeRawData.filter(_ != coffeeHeader)

println(s"咖啡数据读取完成，记录数：${coffeeDataLines.count()}")
println(s"数据表头：$coffeeHeader")

// 解析数据
val coffeeDataRDD = coffeeDataLines.map(parseCoffeeData).filter(_.isDefined).map(_.get)
coffeeDataRDD.cache()

val validCoffeeRecords = coffeeDataRDD.count()
println(s"有效咖啡记录数：$validCoffeeRecords")

// 显示数据示例
println("\n咖啡数据示例（前3条）：")
coffeeDataRDD.take(3).foreach(data => 
  println(s"${data.state} | ${data.product} | 销售额:${data.coffeeSales} | 利润:${data.profit}")
)

// 1. 产品销售排名分析
println("\n=== 产品销售排名分析 ===")
val productSalesRanking = coffeeDataRDD
  .map(data => (data.product, data.coffeeSales))
  .reduceByKey(_ + _)
  .sortBy(_._2, ascending = false)
  .take(10)

println("产品销售额排名（Top 10）：")
println("排名\t产品名称\t\t\t销售额")
productSalesRanking.zipWithIndex.foreach { case ((product, sales), index) =>
  println(f"${index + 1}\t$product%-20s\t${sales.formatted("%.2f")}")
}

// 2. 各州销售分析
println("\n=== 各州销售分析 ===")
val stateSalesRanking = coffeeDataRDD
  .map(data => (data.state, data.coffeeSales))
  .reduceByKey(_ + _)
  .sortBy(_._2, ascending = false)
  .take(10)

println("各州销售额排名（Top 10）：")
println("排名\t州名\t\t销售额")
stateSalesRanking.zipWithIndex.foreach { case ((state, sales), index) =>
  println(f"${index + 1}\t$state%-15s\t${sales.formatted("%.2f")}")
}

// 3. 咖啡类型分析
println("\n=== 咖啡类型分析 ===")
val coffeeTypeAnalysis = coffeeDataRDD
  .map(data => (data.coffeeType, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (coffeeType, (totalSales, totalProfit, count)) =>
    val avgSales = totalSales / count
    val avgProfit = totalProfit / count
    val profitMargin = if (totalSales > 0) totalProfit / totalSales * 100 else 0
    (coffeeType, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count)
  }
  .collect()

println("咖啡类型销售对比：")
println("类型\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
coffeeTypeAnalysis.foreach { case (coffeeType, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count) =>
  println(f"$coffeeType%-8s\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%%\t\t$count")
}

// 4. 市场规模分析
println("\n=== 市场规模分析 ===")
val marketSizeAnalysis = coffeeDataRDD
  .map(data => (data.marketSize, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (marketSize, (totalSales, totalProfit, count)) =>
    val avgSales = totalSales / count
    val avgProfit = totalProfit / count
    val profitMargin = if (totalSales > 0) totalProfit / totalSales * 100 else 0
    (marketSize, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count)
  }
  .collect()

println("市场规模分析：")
println("市场规模\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
marketSizeAnalysis.foreach { case (marketSize, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count) =>
  println(f"$marketSize%-12s\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%%\t\t$count")
}

// 计算总体指标
val totalSales = coffeeDataRDD.map(_.coffeeSales).sum()
val totalProfit = coffeeDataRDD.map(_.profit).sum()
val overallProfitMargin = (totalProfit / totalSales * 100).formatted("%.2f")

println(f"\n=== 总体业务指标 ===")
println(f"总销售额：${totalSales.formatted("%.2f")}")
println(f"总利润：${totalProfit.formatted("%.2f")}")
println(f"整体利润率：$overallProfitMargin%")

// 保存分析结果
println("\n正在保存咖啡分析结果...")

val coffeeResults = sc.parallelize(Seq(
  "=" * 80,
  "咖啡连锁店数据分析报告",
  "=" * 80,
  s"分析时间：${java.time.LocalDateTime.now()}",
  s"数据文件：/home/<USER>/spark/CoffeeChain.csv",
  s"分析记录数：$validCoffeeRecords",
  "",
  "核心业务指标：",
  f"总销售额：${totalSales.formatted("%.2f")}",
  f"总利润：${totalProfit.formatted("%.2f")}",
  f"整体利润率：$overallProfitMargin%",
  "",
  "产品销售排名（Top 5）：",
  productSalesRanking.take(5).zipWithIndex.map { case ((product, sales), index) =>
    f"${index + 1}. $product: ${sales.formatted("%.2f")}"
  }.mkString("\n"),
  "",
  "各州销售排名（Top 5）：",
  stateSalesRanking.take(5).zipWithIndex.map { case ((state, sales), index) =>
    f"${index + 1}. $state: ${sales.formatted("%.2f")}"
  }.mkString("\n"),
  "",
  "咖啡类型分析：",
  coffeeTypeAnalysis.map { case (coffeeType, sales, profit, _, _, profitMargin, _) =>
    f"$coffeeType\t\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%"
  }.mkString("\n")
))

// 清理旧的输出目录
import java.io.File
def deleteDirectory(dir: File): Boolean = {
  if (dir.exists()) {
    val files = dir.listFiles()
    if (files != null) {
      files.foreach(deleteDirectory)
    }
    dir.delete()
  } else {
    true
  }
}

val outputPath = "/home/<USER>/spark/coffee_comprehensive_analysis"
val outputDir = new File(outputPath)
if (outputDir.exists()) {
  deleteDirectory(outputDir)
  println(s"✓ 已清理旧的输出目录：$outputPath")
}

coffeeResults.coalesce(1).saveAsTextFile(outputPath)
println(s"✓ 咖啡分析结果已保存到：$outputPath")

// 验证文件是否成功保存
val resultFile = new File(outputPath + "/part-00000")
if (resultFile.exists()) {
  println(s"✓ 结果文件已生成：${resultFile.getAbsolutePath()}")
  println(s"✓ 文件大小：${resultFile.length()} 字节")
} else {
  println(s"✗ 警告：结果文件未找到")
}

println("\n=== 咖啡数据分析完成 ===")

// 退出spark-shell
System.exit(0)

EOF

echo "咖啡数据分析完成！"
