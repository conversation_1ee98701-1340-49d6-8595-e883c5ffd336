import org.apache.spark.{SparkContext, SparkConf}

/**
 * 简单的Spark测试程序
 * 用于诊断Spark环境和文件保存问题
 */
object TestSpark {
  def main(args: Array[String]): Unit = {
    println("开始Spark测试...")
    
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("Spark Test")
      .setMaster("local[*]")
    
    val sc = new SparkContext(conf)
    
    try {
      println("✓ Spark Context 创建成功")
      
      // 测试基本RDD操作
      val testData = sc.parallelize(1 to 10)
      val sum = testData.reduce(_ + _)
      println(s"✓ 基本RDD操作测试成功，1到10的和为：$sum")
      
      // 测试文件保存
      val testResults = sc.parallelize(Seq(
        "测试结果1",
        "测试结果2", 
        "测试结果3"
      ))
      
      val outputPath = "/home/<USER>/spark/test_output"
      println(s"正在保存测试结果到：$outputPath")
      
      // 删除已存在的输出目录
      import java.io.File
      def deleteDirectory(dir: File): Boolean = {
        if (dir.exists()) {
          val files = dir.listFiles()
          if (files != null) {
            files.foreach(deleteDirectory)
          }
          dir.delete()
        } else {
          true
        }
      }
      
      val outputDir = new File(outputPath)
      if (outputDir.exists()) {
        deleteDirectory(outputDir)
        println("✓ 已清理旧的输出目录")
      }
      
      // 保存结果
      testResults.coalesce(1).saveAsTextFile(outputPath)
      println(s"✓ 测试结果保存成功")
      
      // 验证文件是否存在
      val resultFile = new File(outputPath + "/part-00000")
      if (resultFile.exists()) {
        println(s"✓ 结果文件存在：${resultFile.getAbsolutePath()}")
        println(s"✓ 文件大小：${resultFile.length()} 字节")
        
        // 读取并显示内容
        val source = scala.io.Source.fromFile(resultFile)
        val content = source.getLines().toList
        source.close()
        
        println("✓ 文件内容：")
        content.foreach(line => println(s"  $line"))
      } else {
        println("✗ 结果文件不存在")
      }
      
    } catch {
      case e: Exception =>
        println(s"✗ 测试失败：${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
      println("✓ Spark Context 已关闭")
    }
  }
}
