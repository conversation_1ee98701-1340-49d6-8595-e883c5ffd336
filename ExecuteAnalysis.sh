#!/bin/bash

# Spark大数据分析执行脚本
# 工作目录：/home/<USER>
# 功能：自动化执行人口和咖啡数据分析

echo "=" * 80
echo "Spark大数据分析执行脚本"
echo "工作目录：/home/<USER>"
echo "开始时间：$(date '+%Y-%m-%d %H:%M:%S')"
echo "=" * 80

# 设置工作目录
WORK_DIR="/home/<USER>"
cd $WORK_DIR

# 检查环境函数
check_environment() {
    echo ""
    echo "1. 检查运行环境..."
    echo "----------------------------------------"
    
    # 检查Java
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -1)
        echo "✓ Java环境：$JAVA_VERSION"
    else
        echo "✗ 未找到Java环境，请先安装Java 8或Java 11"
        exit 1
    fi
    
    # 检查Scala
    if command -v scala &> /dev/null; then
        SCALA_VERSION=$(scala -version 2>&1)
        echo "✓ Scala环境：$SCALA_VERSION"
    else
        echo "✗ 未找到Scala环境，请先安装Scala"
        exit 1
    fi
    
    # 检查Spark
    if command -v spark-submit &> /dev/null; then
        echo "✓ Spark环境已安装"
        if command -v spark-shell &> /dev/null; then
            echo "✓ Spark Shell可用"
        fi
    else
        echo "✗ 未找到Spark环境，请先安装Apache Spark"
        exit 1
    fi
    
    echo "✓ 环境检查完成"
}

# 检查数据文件函数
check_data_files() {
    echo ""
    echo "2. 检查数据文件..."
    echo "----------------------------------------"
    
    # 检查咖啡数据文件
    if [ -f "$WORK_DIR/CoffeeChain.csv" ]; then
        FILE_SIZE=$(du -h "$WORK_DIR/CoffeeChain.csv" | cut -f1)
        RECORD_COUNT=$(wc -l < "$WORK_DIR/CoffeeChain.csv")
        echo "✓ 咖啡数据文件：$WORK_DIR/CoffeeChain.csv"
        echo "  文件大小：$FILE_SIZE"
        echo "  记录数：$RECORD_COUNT 行"
    else
        echo "✗ 未找到咖啡数据文件：$WORK_DIR/CoffeeChain.csv"
        echo "  请将CoffeeChain.csv文件放置在 $WORK_DIR 目录下"
        exit 1
    fi
    
    # 检查程序文件
    REQUIRED_FILES=("DataGenerator.scala" "PopulationAnalyzer.scala" "CoffeeDataAnalyzer.scala" "SparkShellCommands.scala")
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$WORK_DIR/$file" ]; then
            echo "✓ 程序文件：$file"
        else
            echo "✗ 缺少程序文件：$file"
            exit 1
        fi
    done
    
    echo "✓ 数据文件检查完成"
}

# 清理旧结果函数
cleanup_old_results() {
    echo ""
    echo "3. 清理旧的分析结果..."
    echo "----------------------------------------"
    
    OLD_RESULTS=("peopleage.txt" "population_analysis_result" "population_analysis_results"
                 "coffee_comprehensive_analysis" "coffee_analysis_results")
    
    for result in "${OLD_RESULTS[@]}"; do
        if [ -e "$WORK_DIR/$result" ]; then
            rm -rf "$WORK_DIR/$result"
            echo "✓ 已清理：$result"
        fi
    done
    
    echo "✓ 清理完成"
}

# 执行人口数据分析函数
execute_population_analysis() {
    echo ""
    echo "4. 执行人口数据分析..."
    echo "----------------------------------------"
    
    # 生成人口数据
    echo "步骤1：生成人口数据..."
    if scala "$WORK_DIR/DataGenerator.scala"; then
        echo "✓ 人口数据生成成功"
        
        if [ -f "$WORK_DIR/peopleage.txt" ]; then
            POPULATION_RECORDS=$(wc -l < "$WORK_DIR/peopleage.txt")
            echo "  生成记录数：$POPULATION_RECORDS 行"
        fi
    else
        echo "✗ 人口数据生成失败"
        return 1
    fi
    
    echo ""
    echo "步骤2：分析人口数据..."
    
    # 编译并运行人口分析程序
    if spark-submit --class PopulationAnalyzer \
                   --master local[*] \
                   --driver-memory 2g \
                   --executor-memory 2g \
                   "$WORK_DIR/PopulationAnalyzer.scala"; then
        echo "✓ 人口数据分析完成"
        
        if [ -d "$WORK_DIR/population_analysis_result" ]; then
            echo "  结果文件：$WORK_DIR/population_analysis_result/"
        fi
    else
        echo "⚠ 人口数据分析可能未完全成功，请检查输出"
    fi
}

# 执行咖啡数据分析函数
execute_coffee_analysis() {
    echo ""
    echo "5. 执行咖啡数据分析..."
    echo "----------------------------------------"
    
    echo "正在分析咖啡连锁店数据..."
    
    # 编译并运行咖啡分析程序
    if spark-submit --class CoffeeDataAnalyzer \
                   --master local[*] \
                   --driver-memory 2g \
                   --executor-memory 2g \
                   --conf spark.sql.adaptive.enabled=true \
                   "$WORK_DIR/CoffeeDataAnalyzer.scala"; then
        echo "✓ 咖啡数据分析完成"
        
        if [ -d "$WORK_DIR/coffee_comprehensive_analysis" ]; then
            echo "  结果文件：$WORK_DIR/coffee_comprehensive_analysis/"
        fi
    else
        echo "⚠ 咖啡数据分析可能未完全成功，请检查输出"
    fi
}

# 显示结果摘要函数
show_results_summary() {
    echo ""
    echo "6. 分析结果摘要"
    echo "----------------------------------------"
    
    echo "生成的数据文件："
    if [ -f "$WORK_DIR/peopleage.txt" ]; then
        POPULATION_SIZE=$(du -h "$WORK_DIR/peopleage.txt" | cut -f1)
        POPULATION_LINES=$(wc -l < "$WORK_DIR/peopleage.txt")
        echo "  ✓ peopleage.txt ($POPULATION_SIZE, $POPULATION_LINES 行)"
    fi
    
    echo ""
    echo "分析结果目录："
    
    RESULT_DIRS=("population_analysis_result" "population_analysis_results" 
                 "coffee_comprehensive_analysis" "coffee_analysis_results")
    
    for dir in "${RESULT_DIRS[@]}"; do
        if [ -d "$WORK_DIR/$dir" ]; then
            echo "  ✓ $dir/"
            if [ -f "$WORK_DIR/$dir/part-00000" ]; then
                echo "    包含结果文件：part-00000"
            fi
        fi
    done
    
    echo ""
    echo "查看结果命令："
    echo "  # 查看人口分析结果"
    echo "  cat $WORK_DIR/population_analysis_result/part-00000"
    echo ""
    echo "  # 查看咖啡分析结果"
    echo "  cat $WORK_DIR/coffee_comprehensive_analysis/part-00000"
    echo ""
    echo "  # 使用Spark Shell查看结果"
    echo "  spark-shell"
    echo "  scala> sc.textFile(\"$WORK_DIR/population_analysis_result/part-00000\").collect().foreach(println)"
    echo "  scala> sc.textFile(\"$WORK_DIR/coffee_comprehensive_analysis/part-00000\").collect().foreach(println)"
}

# 主菜单函数
show_menu() {
    echo ""
    echo "请选择执行方式："
    echo "  1) 完整分析（人口数据 + 咖啡数据）"
    echo "  2) 仅人口数据分析"
    echo "  3) 仅咖啡数据分析"
    echo "  4) 启动Spark Shell（交互式分析）"
    echo "  5) 查看现有结果"
    echo "  6) 退出"
    echo ""
}

# 启动Spark Shell函数
start_spark_shell() {
    echo ""
    echo "启动Spark Shell交互式分析..."
    echo "----------------------------------------"
    echo "提示："
    echo "1. 可以复制粘贴 SparkShellCommands.scala 文件中的代码"
    echo "2. 建议分段执行，先执行人口分析，再执行咖啡分析"
    echo "3. 退出请输入 :quit"
    echo ""
    echo "正在启动Spark Shell..."
    
    cd $WORK_DIR
    spark-shell --driver-memory 2g --executor-memory 2g
}

# 查看现有结果函数
view_existing_results() {
    echo ""
    echo "查看现有分析结果..."
    echo "----------------------------------------"
    
    # 检查并显示人口分析结果
    if [ -f "$WORK_DIR/population_analysis_result/part-00000" ]; then
        echo "人口分析结果："
        echo "=" * 50
        head -20 "$WORK_DIR/population_analysis_result/part-00000"
        echo "=" * 50
        echo ""
    elif [ -f "$WORK_DIR/population_analysis_results/part-00000" ]; then
        echo "人口分析结果："
        echo "=" * 50
        head -20 "$WORK_DIR/population_analysis_results/part-00000"
        echo "=" * 50
        echo ""
    else
        echo "未找到人口分析结果"
    fi
    
    # 检查并显示咖啡分析结果
    if [ -f "$WORK_DIR/coffee_comprehensive_analysis/part-00000" ]; then
        echo "咖啡分析结果："
        echo "=" * 50
        head -20 "$WORK_DIR/coffee_comprehensive_analysis/part-00000"
        echo "=" * 50
        echo ""
    elif [ -f "$WORK_DIR/coffee_analysis_results/part-00000" ]; then
        echo "咖啡分析结果："
        echo "=" * 50
        head -20 "$WORK_DIR/coffee_analysis_results/part-00000"
        echo "=" * 50
        echo ""
    else
        echo "未找到咖啡分析结果"
    fi
}

# 主程序
main() {
    # 检查环境和数据文件
    check_environment
    check_data_files
    
    # 显示菜单并处理用户选择
    while true; do
        show_menu
        read -p "请输入选择 (1-6): " choice
        
        case $choice in
            1)
                cleanup_old_results
                execute_population_analysis
                execute_coffee_analysis
                show_results_summary
                ;;
            2)
                cleanup_old_results
                execute_population_analysis
                show_results_summary
                ;;
            3)
                cleanup_old_results
                execute_coffee_analysis
                show_results_summary
                ;;
            4)
                start_spark_shell
                ;;
            5)
                view_existing_results
                ;;
            6)
                echo "退出程序"
                break
                ;;
            *)
                echo "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 执行主程序
main

echo ""
echo "=" * 80
echo "脚本执行完成"
echo "结束时间：$(date '+%Y-%m-%d %H:%M:%S')"
echo "=" * 80
