# Spark大数据分析项目使用指南

## 项目概述

本项目是一个完整的基于Apache Spark的大数据处理与分析系统，包含人口年龄统计和咖啡连锁店销售数据分析两个模块。

**工作目录：** `/home/<USER>

## 快速开始

### 1. 环境检查

```bash
# 检查Java环境
java -version

# 检查Scala环境  
scala -version

# 检查Spark环境
spark-submit --version
```

### 2. 数据准备

确保以下文件在 `/home/<USER>
- `CoffeeChain.csv` - 咖啡销售数据文件
- 所有 `.scala` 程序文件
- `ExecuteAnalysis.sh` 运行脚本

### 3. 运行分析

**方式一：使用自动化脚本（推荐）**
```bash
cd /home/<USER>
chmod +x ExecuteAnalysis.sh
./ExecuteAnalysis.sh
```

**方式二：使用Spark Shell（交互式）**
```bash
cd /home/<USER>
spark-shell --driver-memory 2g --executor-memory 2g
# 然后复制粘贴 SparkShellCommands.scala 中的代码
```

## 项目结构

```
/home/<USER>/
├── 数据文件
│   ├── CoffeeChain.csv              # 咖啡销售数据（需要提供）
│   └── population_data.txt          # 生成的人口数据（运行后产生）
│
├── 程序文件
│   ├── DataGenerator.scala          # 人口数据生成器
│   ├── PopulationAnalyzer.scala     # 人口数据分析器
│   ├── CoffeeDataAnalyzer.scala     # 咖啡数据分析器
│   └── SparkShellCommands.scala     # Spark Shell交互式代码
│
├── 脚本文件
│   └── ExecuteAnalysis.sh           # 自动化运行脚本
│
├── 文档文件
│   ├── 大数据分析实训报告.md         # 完整实训报告
│   └── 项目使用指南.md              # 本文件
│
└── 结果文件（运行后生成）
    ├── population_analysis_result/   # 人口分析结果
    ├── coffee_comprehensive_analysis/ # 咖啡分析结果
    └── 其他分析结果目录
```

## 功能模块详解

### 模块一：人口年龄数据分析

**功能**：
- 生成1,500条模拟人口数据
- 多维度统计分析（年龄、性别、城市）
- 年龄段分布分析

**运行方式**：
```bash
# 单独运行人口分析
scala DataGenerator.scala
spark-submit --class PopulationAnalyzer --master local[*] PopulationAnalyzer.scala
```

**输出结果**：
- `population_data.txt` - 生成的人口数据
- `population_analysis_result/` - 分析结果目录

### 模块二：咖啡连锁店数据分析

**功能**：
- 销售排名分析
- 市场分布分析
- 盈利能力分析
- 产品性能分析
- 区域分析
- 时间序列分析
- 业务洞察分析

**运行方式**：
```bash
# 单独运行咖啡分析
spark-submit --class CoffeeDataAnalyzer --master local[*] CoffeeDataAnalyzer.scala
```

**输出结果**：
- `coffee_comprehensive_analysis/` - 综合分析结果

## 运行选项说明

### ExecuteAnalysis.sh 脚本选项

1. **完整分析**：运行人口数据分析 + 咖啡数据分析
2. **仅人口数据分析**：只运行人口相关分析
3. **仅咖啡数据分析**：只运行咖啡相关分析
4. **启动Spark Shell**：进入交互式分析模式
5. **查看现有结果**：显示已生成的分析结果
6. **退出**：退出脚本

### Spark Shell 交互式分析

**优势**：
- 可以分段执行代码
- 实时查看中间结果
- 便于调试和学习
- 支持动态修改参数

**使用方法**：
1. 启动：`spark-shell --driver-memory 2g --executor-memory 2g`
2. 复制粘贴 `SparkShellCommands.scala` 中的代码
3. 分段执行，观察结果

## 结果查看

### 查看分析结果

```bash
# 查看人口分析结果
cat /home/<USER>/population_analysis_result/part-00000

# 查看咖啡分析结果  
cat /home/<USER>/coffee_comprehensive_analysis/part-00000

# 查看生成的人口数据示例
head -10 /home/<USER>/population_data.txt
```

### 在Spark Shell中查看结果

```scala
// 查看人口分析结果
sc.textFile("/home/<USER>/population_analysis_result/part-00000").collect().foreach(println)

// 查看咖啡分析结果
sc.textFile("/home/<USER>/coffee_comprehensive_analysis/part-00000").collect().foreach(println)
```

## 性能调优建议

### 内存配置

```bash
# 根据系统配置调整内存参数
spark-shell --driver-memory 4g --executor-memory 4g

# 启用自适应查询执行
spark-shell --conf spark.sql.adaptive.enabled=true
```

### 常见配置

```bash
# 完整的性能优化配置
spark-shell \
  --driver-memory 4g \
  --executor-memory 4g \
  --conf spark.sql.adaptive.enabled=true \
  --conf spark.sql.adaptive.coalescePartitions.enabled=true \
  --conf spark.serializer=org.apache.spark.serializer.KryoSerializer
```

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 解决方案：增加内存配置
   spark-shell --driver-memory 4g --executor-memory 4g
   ```

2. **找不到文件**
   ```bash
   # 检查文件是否存在
   ls -la /home/<USER>/CoffeeChain.csv
   ```

3. **权限问题**
   ```bash
   # 设置正确权限
   chmod 755 /home/<USER>
   chmod +x /home/<USER>/*.sh
   ```

4. **Java版本问题**
   ```bash
   # 检查Java版本
   java -version
   # 确保使用Java 8或Java 11
   ```

### 环境问题解决

```bash
# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk
export SCALA_HOME=/opt/scala
export SPARK_HOME=/opt/spark
export PATH=$JAVA_HOME/bin:$SCALA_HOME/bin:$SPARK_HOME/bin:$PATH
```

## 扩展功能

### 自定义分析

可以基于现有框架进行扩展：

1. **修改数据生成参数**：
   - 调整人口数据的记录数量
   - 修改年龄范围和城市列表

2. **添加新的分析维度**：
   - 增加新的统计指标
   - 添加数据可视化功能

3. **优化性能**：
   - 调整分区策略
   - 使用更高效的算法

### 集成其他工具

- **数据可视化**：集成Matplotlib、Plotly等
- **机器学习**：使用Spark MLlib进行预测分析
- **实时处理**：集成Spark Streaming处理实时数据

## 学习建议

### 初学者

1. 先运行 `SparkShellCommands.scala` 了解基本流程
2. 逐步理解每个RDD操作的含义
3. 观察中间结果，理解数据转换过程

### 进阶用户

1. 研究性能优化技巧
2. 尝试修改分析逻辑
3. 扩展新的分析功能

### 实际应用

1. 使用真实业务数据进行分析
2. 结合业务场景解读分析结果
3. 将分析结果应用到决策中

## 技术支持

### 文档资源

- `大数据分析实训报告.md` - 详细的技术文档和分析结果
- 代码注释 - 每个程序文件都有详细注释
- Spark官方文档 - https://spark.apache.org/docs/

### 常用命令参考

```bash
# Spark Shell常用命令
:help          # 显示帮助
:quit          # 退出
:paste         # 粘贴模式
:load file     # 加载文件

# 系统监控
top            # 查看系统资源使用
df -h          # 查看磁盘空间
free -h        # 查看内存使用
```

## 项目特色

1. **完整的分析流程**：从数据生成到深度分析
2. **多种运行方式**：脚本自动化、交互式分析
3. **详细的文档**：完整的实训报告和使用指南
4. **实用的案例**：人口统计和商业分析两个实际场景
5. **可扩展性**：模块化设计，便于扩展和修改

---

**注意事项**：
- 确保有足够的磁盘空间存储结果文件
- 建议在虚拟机中至少分配4GB内存
- 运行前请备份重要数据
- 如遇到问题，请查看控制台输出的错误信息

**联系方式**：
如有技术问题，请参考实训报告中的问题解决方案部分，或查阅Spark官方文档。
