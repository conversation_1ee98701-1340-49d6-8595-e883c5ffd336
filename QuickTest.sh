#!/bin/bash

# 快速测试脚本 - 诊断Spark分析结果生成问题
echo "快速诊断Spark分析问题..."

WORK_DIR="/home/<USER>/spark"
cd $WORK_DIR

echo "1. 检查工作目录和权限..."
echo "当前目录：$(pwd)"
echo "目录权限：$(ls -ld .)"
echo ""

echo "2. 清理旧结果..."
rm -rf population_analysis_result coffee_comprehensive_analysis peopleage.txt test_output
echo "✓ 旧结果已清理"
echo ""

echo "3. 测试基本Spark功能..."
if [ -f "TestSpark.scala" ]; then
    echo "运行Spark基础测试..."
    if scala TestSpark.scala 2>&1; then
        echo "✓ Spark基础测试通过"
        if [ -f "test_output/part-00000" ]; then
            echo "✓ Spark文件保存功能正常"
            echo "测试文件内容："
            cat test_output/part-00000
        else
            echo "✗ Spark文件保存功能异常"
        fi
    else
        echo "✗ Spark基础测试失败"
    fi
else
    echo "✗ TestSpark.scala 不存在"
fi
echo ""

echo "4. 测试人口数据生成..."
if scala DataGenerator.scala; then
    echo "✓ 人口数据生成成功"
    if [ -f "peopleage.txt" ]; then
        echo "✓ peopleage.txt 已生成"
        echo "文件大小：$(wc -l < peopleage.txt) 行"
        echo "前3行内容："
        head -3 peopleage.txt
    else
        echo "✗ peopleage.txt 未生成"
    fi
else
    echo "✗ 人口数据生成失败"
fi
echo ""

echo "5. 测试人口数据分析..."
if spark-submit --class PopulationAnalyzer --master local[*] PopulationAnalyzer.scala 2>&1; then
    echo "✓ 人口数据分析程序执行完成"
    
    if [ -d "population_analysis_result" ]; then
        echo "✓ 分析结果目录已生成"
        echo "目录内容："
        ls -la population_analysis_result/
        
        if [ -f "population_analysis_result/part-00000" ]; then
            echo "✓ 结果文件存在"
            echo "文件大小：$(wc -l < population_analysis_result/part-00000) 行"
            echo "文件内容："
            cat population_analysis_result/part-00000
        else
            echo "✗ 结果文件不存在"
        fi
    else
        echo "✗ 分析结果目录未生成"
    fi
else
    echo "✗ 人口数据分析失败"
fi
echo ""

echo "6. 检查咖啡数据文件..."
if [ -f "CoffeeChain.csv" ]; then
    echo "✓ CoffeeChain.csv 存在"
    echo "文件大小：$(wc -l < CoffeeChain.csv) 行"
    echo "文件头部："
    head -2 CoffeeChain.csv
    
    echo ""
    echo "7. 测试咖啡数据分析..."
    if spark-submit --class CoffeeDataAnalyzer --master local[*] CoffeeDataAnalyzer.scala 2>&1; then
        echo "✓ 咖啡数据分析程序执行完成"
        
        if [ -d "coffee_comprehensive_analysis" ]; then
            echo "✓ 咖啡分析结果目录已生成"
            echo "目录内容："
            ls -la coffee_comprehensive_analysis/
            
            if [ -f "coffee_comprehensive_analysis/part-00000" ]; then
                echo "✓ 咖啡结果文件存在"
                echo "文件大小：$(wc -l < coffee_comprehensive_analysis/part-00000) 行"
                echo "文件前10行："
                head -10 coffee_comprehensive_analysis/part-00000
            else
                echo "✗ 咖啡结果文件不存在"
            fi
        else
            echo "✗ 咖啡分析结果目录未生成"
        fi
    else
        echo "✗ 咖啡数据分析失败"
    fi
else
    echo "✗ CoffeeChain.csv 不存在，跳过咖啡数据分析"
fi
echo ""

echo "8. 最终状态检查..."
echo "当前目录文件："
ls -la
echo ""

echo "分析结果目录："
for dir in population_analysis_result coffee_comprehensive_analysis; do
    if [ -d "$dir" ]; then
        echo "✓ $dir 存在"
        if [ -f "$dir/part-00000" ]; then
            echo "  ✓ 结果文件存在，大小：$(du -h $dir/part-00000 | cut -f1)"
        else
            echo "  ✗ 结果文件不存在"
        fi
    else
        echo "✗ $dir 不存在"
    fi
done

echo ""
echo "诊断完成！"
echo ""
echo "如果看到 ✓ 表示正常，✗ 表示有问题"
echo "请检查上面的输出，找出问题所在。"
