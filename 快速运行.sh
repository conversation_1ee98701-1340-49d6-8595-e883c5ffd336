#!/bin/bash

# 快速运行脚本 - 专为 /home/<USER>/me 路径优化
# 使用方法：chmod +x 快速运行.sh && ./快速运行.sh

echo "=== Spark数据处理实训快速运行脚本 ==="
echo "工作目录：/home/<USER>/me"
echo "开始时间：$(date)"
echo ""

# 切换到工作目录
cd /home/<USER>/me

# 检查必要文件
echo "1. 检查必要文件..."
missing_files=()

if [ ! -f "/home/<USER>/me/CoffeeChain.csv" ]; then
    missing_files+=("CoffeeChain.csv")
fi

if [ ! -f "/home/<USER>/me/spark_commands.scala" ]; then
    missing_files+=("spark_commands.scala")
fi

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "✗ 缺少以下文件："
    for file in "${missing_files[@]}"; do
        echo "  - $file"
    done
    echo ""
    echo "请确保以下文件在 /home/<USER>/me/ 目录下："
    echo "  - CoffeeChain.csv (咖啡数据文件)"
    echo "  - spark_commands.scala (Spark命令文件)"
    echo "  - 其他 .scala 程序文件"
    exit 1
else
    echo "✓ 所有必要文件都存在"
fi

echo ""

# 检查环境
echo "2. 检查运行环境..."

# 检查Java
if command -v java &> /dev/null; then
    echo "✓ Java环境：$(java -version 2>&1 | head -1)"
else
    echo "✗ 未找到Java环境"
    exit 1
fi

# 检查Scala
if command -v scala &> /dev/null; then
    echo "✓ Scala环境：$(scala -version 2>&1)"
else
    echo "✗ 未找到Scala环境"
    exit 1
fi

# 检查Spark
if command -v spark-shell &> /dev/null; then
    echo "✓ Spark环境已安装"
else
    echo "✗ 未找到Spark环境"
    exit 1
fi

echo ""

# 提供运行选项
echo "3. 选择运行方式："
echo "   1) 使用spark-shell运行（推荐，简单易用）"
echo "   2) 编译并运行Scala程序（完整版本）"
echo "   3) 只生成人口数据文件"
echo "   4) 查看已有结果文件"
echo ""

read -p "请选择运行方式 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "=== 启动spark-shell模式 ==="
        echo "请按以下步骤操作："
        echo ""
        echo "1. spark-shell启动后，复制粘贴以下文件中的代码："
        echo "   /home/<USER>/me/spark_commands.scala"
        echo ""
        echo "2. 分段执行代码（建议先执行人口分析部分，再执行咖啡分析部分）"
        echo ""
        echo "3. 执行完成后，结果将保存在："
        echo "   - /home/<USER>/me/people_age_analysis/"
        echo "   - /home/<USER>/me/coffee_analysis_results/"
        echo ""
        echo "正在启动spark-shell..."
        echo "提示：退出spark-shell请输入 :quit"
        echo ""
        spark-shell
        ;;
    2)
        echo ""
        echo "=== 编译运行模式 ==="
        
        # 生成人口数据
        echo "步骤1: 生成人口年龄数据..."
        if [ -f "/home/<USER>/me/GeneratePeopleAge.scala" ]; then
            scala /home/<USER>/me/GeneratePeopleAge.scala
            if [ -f "/home/<USER>/me/peopleage.txt" ]; then
                echo "✓ 人口数据生成成功"
            else
                echo "✗ 人口数据生成失败"
                exit 1
            fi
        else
            echo "✗ 未找到 GeneratePeopleAge.scala"
            exit 1
        fi
        
        echo ""
        
        # 计算平均年龄
        echo "步骤2: 计算平均年龄..."
        if [ -f "/home/<USER>/me/CalculateAverageAge.scala" ]; then
            echo "正在编译和运行..."
            spark-submit --class CalculateAverageAge --master local[*] /home/<USER>/me/CalculateAverageAge.scala
            if [ -d "/home/<USER>/me/average_age_result.txt" ]; then
                echo "✓ 平均年龄计算完成"
            fi
        else
            echo "✗ 未找到 CalculateAverageAge.scala"
        fi
        
        echo ""
        
        # 咖啡数据分析
        echo "步骤3: 咖啡数据分析..."
        if [ -f "/home/<USER>/me/CoffeeChainAnalysis.scala" ]; then
            echo "正在编译和运行..."
            spark-submit --class CoffeeChainAnalysis --master local[*] /home/<USER>/me/CoffeeChainAnalysis.scala
            if [ -d "/home/<USER>/me/coffee_sales_ranking" ]; then
                echo "✓ 咖啡数据分析完成"
            fi
        else
            echo "✗ 未找到 CoffeeChainAnalysis.scala"
        fi
        ;;
    3)
        echo ""
        echo "=== 生成人口数据 ==="
        if [ -f "/home/<USER>/me/GeneratePeopleAge.scala" ]; then
            scala /home/<USER>/me/GeneratePeopleAge.scala
            if [ -f "/home/<USER>/me/peopleage.txt" ]; then
                echo "✓ 人口数据生成成功"
                echo "文件位置：/home/<USER>/me/peopleage.txt"
                echo "记录数：$(wc -l < /home/<USER>/me/peopleage.txt)"
                echo ""
                echo "数据示例："
                head -5 /home/<USER>/me/peopleage.txt
            fi
        else
            echo "✗ 未找到 GeneratePeopleAge.scala"
        fi
        ;;
    4)
        echo ""
        echo "=== 查看结果文件 ==="
        echo ""
        
        # 检查人口数据
        if [ -f "/home/<USER>/me/peopleage.txt" ]; then
            echo "✓ 人口数据文件：/home/<USER>/me/peopleage.txt"
            echo "  记录数：$(wc -l < /home/<USER>/me/peopleage.txt)"
        else
            echo "✗ 未找到人口数据文件"
        fi
        
        # 检查分析结果目录
        result_dirs=(
            "/home/<USER>/me/people_age_analysis"
            "/home/<USER>/me/coffee_analysis_results"
            "/home/<USER>/me/average_age_result.txt"
            "/home/<USER>/me/coffee_sales_ranking"
            "/home/<USER>/me/coffee_distribution_analysis"
        )
        
        echo ""
        echo "分析结果目录："
        for dir in "${result_dirs[@]}"; do
            if [ -d "$dir" ]; then
                echo "✓ $dir"
                if [ -f "$dir/part-00000" ]; then
                    echo "  包含结果文件：part-00000"
                fi
            else
                echo "✗ $dir (未找到)"
            fi
        done
        
        echo ""
        echo "要查看具体结果，请运行："
        echo "cat /home/<USER>/me/people_age_analysis/part-00000"
        echo "cat /home/<USER>/me/coffee_analysis_results/part-00000"
        ;;
    *)
        echo "无效选择，退出"
        exit 1
        ;;
esac

echo ""
echo "=== 运行完成 ==="
echo "完成时间：$(date)"

# 显示结果摘要
echo ""
echo "生成的文件和目录："
ls -la /home/<USER>/me/ | grep -E "(\.txt|_analysis|_ranking|_result)"

echo ""
echo "如需查看详细结果，请使用以下命令："
echo "cat /home/<USER>/me/people_age_analysis/part-00000      # 人口分析结果"
echo "cat /home/<USER>/me/coffee_analysis_results/part-00000  # 咖啡分析结果"
