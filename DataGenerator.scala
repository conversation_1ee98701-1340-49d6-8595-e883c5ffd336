import scala.util.Random
import java.io.{PrintWriter, File}
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * 人口年龄数据生成器
 * 功能：生成指定数量的模拟人口年龄数据
 * 输出格式：ID,姓名,年龄,性别,城市
 * 工作目录：/home/<USER>
 */
object DataGenerator {
  
  // 预定义的姓名列表
  val firstNames = Array("张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴",
                        "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗")
  
  val secondNames = Array("伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军",
                         "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞")
  
  val cities = Array("北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都", 
                    "西安", "重庆", "天津", "苏州", "长沙", "郑州", "青岛")
  
  val genders = Array("男", "女")
  
  def main(args: Array[String]): Unit = {
    // 配置参数
    val recordCount = 1500  // 生成1500条记录
    val minAge = 16         // 最小年龄
    val maxAge = 85         // 最大年龄
    val outputPath = "/home/<USER>/population_data.txt"
    
    println("=" * 60)
    println("人口年龄数据生成器")
    println("=" * 60)
    println(s"生成记录数：$recordCount")
    println(s"年龄范围：$minAge - $maxAge 岁")
    println(s"输出文件：$outputPath")
    println(s"开始时间：${getCurrentTime()}")
    println()
    
    val random = new Random()
    val writer = new PrintWriter(new File(outputPath))
    
    try {
      // 写入文件头
      writer.println("ID,姓名,年龄,性别,城市")
      
      // 生成数据
      for (i <- 1 to recordCount) {
        val id = f"P$i%06d"  // 格式化ID，如P000001
        val firstName = firstNames(random.nextInt(firstNames.length))
        val secondName = secondNames(random.nextInt(secondNames.length))
        val name = firstName + secondName
        val age = minAge + random.nextInt(maxAge - minAge + 1)
        val gender = genders(random.nextInt(genders.length))
        val city = cities(random.nextInt(cities.length))
        
        writer.println(s"$id,$name,$age,$gender,$city")
        
        // 显示进度
        if (i % 200 == 0) {
          val progress = (i.toDouble / recordCount * 100).formatted("%.1f")
          println(s"已生成 $i 条记录... ($progress%)")
        }
      }
      
      println()
      println("✓ 数据生成完成！")
      println(s"文件保存位置：$outputPath")
      println(s"完成时间：${getCurrentTime()}")
      
    } catch {
      case e: Exception =>
        println(s"✗ 数据生成失败：${e.getMessage}")
        e.printStackTrace()
    } finally {
      writer.close()
    }
    
    // 显示数据统计和示例
    showDataSummary(outputPath)
  }
  
  /**
   * 获取当前时间字符串
   */
  def getCurrentTime(): String = {
    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
  }
  
  /**
   * 显示数据摘要和示例
   */
  def showDataSummary(filePath: String): Unit = {
    try {
      val source = scala.io.Source.fromFile(filePath)
      val lines = source.getLines().toList
      source.close()
      
      println()
      println("=" * 40)
      println("数据摘要")
      println("=" * 40)
      println(s"总记录数：${lines.length - 1} 条（不含表头）")
      println()
      println("数据示例（前10行）：")
      println("-" * 40)
      lines.take(11).foreach(println)  // 包含表头的前11行
      println("-" * 40)
      
      // 简单统计
      val dataLines = lines.tail  // 去掉表头
      val ages = dataLines.map(_.split(",")(2).toInt)
      val avgAge = ages.sum.toDouble / ages.length
      val maxAge = ages.max
      val minAge = ages.min
      
      println()
      println("年龄统计：")
      println(f"平均年龄：$avgAge%.2f 岁")
      println(s"最大年龄：$maxAge 岁")
      println(s"最小年龄：$minAge 岁")
      
    } catch {
      case e: Exception =>
        println(s"读取文件时出错：${e.getMessage}")
    }
  }
}
