import scala.util.Random
import java.io.{PrintWriter, File}

/**
 * 人口年龄数据生成器
 * 功能：生成指定数量的人口年龄数据
 * 输出格式：序号    年龄
 * 工作目录：/home/<USER>
 */
object DataGenerator {

  def main(args: Array[String]): Unit = {
    // 配置参数
    val recordCount = 1000  // 生成1000条记录
    val minAge = 18         // 最小年龄
    val maxAge = 90         // 最大年龄
    val outputPath = "/home/<USER>/peopleage.txt"
    
    println("=" * 50)
    println("人口年龄数据生成器")
    println("=" * 50)
    println(s"生成记录数：$recordCount")
    println(s"年龄范围：$minAge - $maxAge 岁")
    println(s"输出文件：$outputPath")
    println(s"数据格式：序号    年龄")
    println()

    val random = new Random()
    val writer = new PrintWriter(new File(outputPath))

    try {
      // 生成数据（不写表头，直接写数据）
      for (i <- 1 to recordCount) {
        val age = minAge + random.nextInt(maxAge - minAge + 1)
        writer.println(s"$i\t$age")  // 使用制表符分隔

        // 显示进度
        if (i % 100 == 0) {
          val progress = (i.toDouble / recordCount * 100).formatted("%.1f")
          println(s"已生成 $i 条记录... ($progress%)")
        }
      }
      
      println()
      println("✓ 数据生成完成！")
      println(s"文件保存位置：$outputPath")

    } catch {
      case e: Exception =>
        println(s"✗ 数据生成失败：${e.getMessage}")
        e.printStackTrace()
    } finally {
      writer.close()
    }

    // 显示数据统计和示例
    showDataSummary(outputPath)
  }

  /**
   * 显示数据摘要和示例
   */
  def showDataSummary(filePath: String): Unit = {
    try {
      val source = scala.io.Source.fromFile(filePath)
      val lines = source.getLines().toList
      source.close()

      println()
      println("=" * 40)
      println("数据摘要")
      println("=" * 40)
      println(s"总记录数：${lines.length} 条")
      println()
      println("数据示例（前10行）：")
      println("-" * 20)
      lines.take(10).foreach(println)
      println("-" * 20)

      // 简单统计
      val ages = lines.map(line => line.split("\t")(1).toInt)
      val avgAge = ages.sum.toDouble / ages.length
      val maxAge = ages.max
      val minAge = ages.min

      println()
      println("年龄统计：")
      println(f"平均年龄：$avgAge%.2f 岁")
      println(s"最大年龄：$maxAge 岁")
      println(s"最小年龄：$minAge 岁")

    } catch {
      case e: Exception =>
        println(s"读取文件时出错：${e.getMessage}")
    }
  }
}
