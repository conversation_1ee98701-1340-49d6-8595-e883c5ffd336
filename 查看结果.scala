// 在spark-shell中运行此代码来查看分析结果

println("=== 查看分析结果 ===")

// 检查文件是否存在
import java.io.File

def checkFile(path: String): Boolean = {
  val file = new File(path)
  file.exists()
}

println("\n1. 检查结果文件是否存在...")

val peopleAnalysisPath = "/home/<USER>/me/people_age_analysis"
val coffeeAnalysisPath = "/home/<USER>/me/coffee_analysis_results"

if (checkFile(peopleAnalysisPath)) {
  println(s"✓ 人口分析结果存在：$peopleAnalysisPath")
} else {
  println(s"✗ 人口分析结果不存在：$peopleAnalysisPath")
}

if (checkFile(coffeeAnalysisPath)) {
  println(s"✓ 咖啡分析结果存在：$coffeeAnalysisPath")
} else {
  println(s"✗ 咖啡分析结果不存在：$coffeeAnalysisPath")
}

println("\n2. 查看人口年龄分析结果...")
try {
  val peopleResults = sc.textFile("/home/<USER>/me/people_age_analysis/part-00000")
  println("人口年龄分析结果：")
  println("=" * 50)
  peopleResults.collect().foreach(println)
  println("=" * 50)
} catch {
  case e: Exception =>
    println(s"读取人口分析结果时出错：${e.getMessage}")
}

println("\n3. 查看咖啡数据分析结果...")
try {
  val coffeeResults = sc.textFile("/home/<USER>/me/coffee_analysis_results/part-00000")
  println("咖啡数据分析结果：")
  println("=" * 50)
  coffeeResults.collect().foreach(println)
  println("=" * 50)
} catch {
  case e: Exception =>
    println(s"读取咖啡分析结果时出错：${e.getMessage}")
}

println("\n4. 查看生成的数据文件...")
try {
  val peopleData = sc.textFile("/home/<USER>/me/peopleage.txt")
  println(s"人口数据总记录数：${peopleData.count()}")
  println("人口数据示例（前10行）：")
  peopleData.take(10).foreach(println)
} catch {
  case e: Exception =>
    println(s"读取人口数据时出错：${e.getMessage}")
}

println("\n=== 结果查看完成 ===")
