import org.apache.spark.{SparkContext, SparkConf}
import org.apache.spark.rdd.RDD

/**
 * 咖啡连锁店数据分析系统
 * 全面分析咖啡销售数据，包括多维度统计和业务洞察
 * 工作目录：/home/<USER>
 */
object CoffeeDataAnalyzer {
  
  // 咖啡销售数据模型
  case class CoffeeSalesData(
    areaCode: String,
    date: String,
    market: String,
    marketSize: String,
    product: String,
    productType: String,
    state: String,
    coffeeType: String,
    budgetCogs: Double,
    budgetMargin: Double,
    budgetProfit: Double,
    budgetSales: Double,
    coffeeSales: Double,
    cogs: Double,
    inventory: Double,
    margin: Double,
    marketing: Double,
    numberOfRecords: Int,
    profit: Double,
    totalExpenses: Double
  )
  
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("Coffee Chain Data Analyzer")
      .setMaster("local[*]")
      .set("spark.sql.adaptive.enabled", "true")
      .set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
    
    val sc = new SparkContext(conf)
    
    try {
      println("=" * 80)
      println("咖啡连锁店大数据分析系统")
      println("=" * 80)
      
      // 数据预处理
      val coffeeDataRDD = loadAndPreprocessData(sc)
      
      // 执行各种分析
      performSalesRankingAnalysis(coffeeDataRDD)
      performMarketAnalysis(coffeeDataRDD)
      performProfitabilityAnalysis(coffeeDataRDD)
      performProductPerformanceAnalysis(coffeeDataRDD)
      performRegionalAnalysis(coffeeDataRDD)
      performTimeSeriesAnalysis(coffeeDataRDD)
      performBusinessInsights(coffeeDataRDD)
      
      // 保存综合分析结果
      saveComprehensiveResults(coffeeDataRDD, sc)
      
      println("\n" + "=" * 80)
      println("✓ 咖啡数据分析完成！")
      println("=" * 80)
      
    } catch {
      case e: Exception =>
        println(s"✗ 分析过程出错：${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
  
  /**
   * 数据加载和预处理
   */
  def loadAndPreprocessData(sc: SparkContext): RDD[CoffeeSalesData] = {
    println("\n" + "=" * 60)
    println("1. 数据预处理阶段")
    println("=" * 60)
    
    val inputFile = "/home/<USER>/CoffeeChain.csv"
    println(s"正在加载数据文件：$inputFile")
    
    val rawData = sc.textFile(inputFile)
    val header = rawData.first()
    val dataLines = rawData.filter(_ != header)
    
    println(s"原始数据记录数：${dataLines.count()}")
    println("数据表头字段：")
    header.split(",").zipWithIndex.foreach { case (field, index) =>
      println(f"  $index%2d. $field")
    }
    
    // 解析和清洗数据
    val coffeeDataRDD = dataLines.map(parseCoffeeData).filter(_.isDefined).map(_.get)
    coffeeDataRDD.cache()
    
    val validRecords = coffeeDataRDD.count()
    val invalidRecords = dataLines.count() - validRecords
    
    println(f"\n数据清洗结果：")
    println(f"有效记录：$validRecords 条")
    println(f"无效记录：$invalidRecords 条")
    println(f"数据质量：${(validRecords.toDouble / dataLines.count() * 100).formatted("%.2f")}%")
    
    // 显示数据示例
    println(f"\n数据示例（前3条）：")
    coffeeDataRDD.take(3).foreach { data =>
      println(f"${data.state} | ${data.product} | ${data.coffeeType} | 销售额:${data.coffeeSales.formatted("%.2f")} | 利润:${data.profit.formatted("%.2f")}")
    }
    
    coffeeDataRDD
  }
  
  /**
   * 解析CSV数据行
   */
  def parseCoffeeData(line: String): Option[CoffeeSalesData] = {
    try {
      val parts = line.split(",")
      if (parts.length >= 20) {
        Some(CoffeeSalesData(
          areaCode = parts(0),
          date = parts(1),
          market = parts(2),
          marketSize = parts(3),
          product = parts(4),
          productType = parts(5),
          state = parts(6),
          coffeeType = parts(7),
          budgetCogs = parseDouble(parts(8)),
          budgetMargin = parseDouble(parts(9)),
          budgetProfit = parseDouble(parts(10)),
          budgetSales = parseDouble(parts(11)),
          coffeeSales = parseDouble(parts(12)),
          cogs = parseDouble(parts(13)),
          inventory = parseDouble(parts(14)),
          margin = parseDouble(parts(15)),
          marketing = parseDouble(parts(16)),
          numberOfRecords = parseInt(parts(17)),
          profit = parseDouble(parts(18)),
          totalExpenses = parseDouble(parts(19))
        ))
      } else None
    } catch {
      case _: Exception => None
    }
  }
  
  /**
   * 安全解析Double类型
   */
  def parseDouble(str: String): Double = {
    try {
      str.replaceAll("\"", "").replaceAll(",", "").toDouble
    } catch {
      case _: Exception => 0.0
    }
  }
  
  /**
   * 安全解析Int类型
   */
  def parseInt(str: String): Int = {
    try {
      str.replaceAll("\"", "").replaceAll(",", "").toInt
    } catch {
      case _: Exception => 0
    }
  }
  
  /**
   * 销售排名分析
   */
  def performSalesRankingAnalysis(coffeeDataRDD: RDD[CoffeeSalesData]): Unit = {
    println("\n" + "=" * 60)
    println("2. 销售排名分析")
    println("=" * 60)
    
    // 产品销售排名
    val productSalesRanking = coffeeDataRDD
      .map(data => (data.product, data.coffeeSales))
      .reduceByKey(_ + _)
      .sortBy(_._2, ascending = false)
      .take(15)
    
    println("产品销售额排名（Top 15）：")
    println("排名\t产品名称\t\t\t销售额")
    productSalesRanking.zipWithIndex.foreach { case ((product, sales), index) =>
      println(f"${index + 1}%2d\t$product%-25s\t${sales.formatted("%.2f")}")
    }
    
    // 州销售排名
    val stateSalesRanking = coffeeDataRDD
      .map(data => (data.state, data.coffeeSales))
      .reduceByKey(_ + _)
      .sortBy(_._2, ascending = false)
      .collect()
    
    println(f"\n各州销售额排名：")
    println("排名\t州名\t\t销售额\t\t市场份额")
    val totalSales = stateSalesRanking.map(_._2).sum
    stateSalesRanking.zipWithIndex.foreach { case ((state, sales), index) =>
      val marketShare = (sales / totalSales * 100).formatted("%.2f")
      println(f"${index + 1}%2d\t$state%-12s\t${sales.formatted("%.2f")}\t\t$marketShare%")
    }
    
    // 咖啡类型销售排名
    val coffeeTypeSales = coffeeDataRDD
      .map(data => (data.coffeeType, data.coffeeSales))
      .reduceByKey(_ + _)
      .sortBy(_._2, ascending = false)
      .collect()
    
    println(f"\n咖啡类型销售排名：")
    coffeeTypeSales.foreach { case (coffeeType, sales) =>
      val share = (sales / totalSales * 100).formatted("%.2f")
      println(f"$coffeeType: ${sales.formatted("%.2f")} ($share%)")
    }
  }
  
  /**
   * 市场分析
   */
  def performMarketAnalysis(coffeeDataRDD: RDD[CoffeeSalesData]): Unit = {
    println("\n" + "=" * 60)
    println("3. 市场分析")
    println("=" * 60)
    
    // 市场规模分析
    val marketSizeAnalysis = coffeeDataRDD
      .map(data => (data.marketSize, (data.coffeeSales, data.profit, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case (marketSize, (totalSales, totalProfit, count)) =>
        val avgSales = totalSales / count
        val avgProfit = totalProfit / count
        val profitMargin = if (totalSales > 0) totalProfit / totalSales * 100 else 0
        (marketSize, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count)
      }
      .collect()
    
    println("市场规模分析：")
    println("市场规模\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
    marketSizeAnalysis.foreach { case (size, totalSales, totalProfit, avgSales, avgProfit, margin, count) =>
      println(f"$size%-15s\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${margin.formatted("%.2f")}%\t\t$count")
    }
    
    // 地理市场分析
    val geographicMarketAnalysis = coffeeDataRDD
      .map(data => (data.market, (data.coffeeSales, data.profit, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case (market, (totalSales, totalProfit, count)) =>
        val avgSales = totalSales / count
        val avgProfit = totalProfit / count
        (market, totalSales, totalProfit, avgSales, avgProfit, count)
      }
      .sortBy(_._2, ascending = false)
      .collect()
    
    println(f"\n地理市场分析：")
    println("市场区域\t总销售额\t总利润\t\t平均销售额\t平均利润\t记录数")
    geographicMarketAnalysis.foreach { case (market, totalSales, totalProfit, avgSales, avgProfit, count) =>
      println(f"$market%-8s\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t$count")
    }
  }
  
  /**
   * 盈利能力分析
   */
  def performProfitabilityAnalysis(coffeeDataRDD: RDD[CoffeeSalesData]): Unit = {
    println("\n" + "=" * 60)
    println("4. 盈利能力分析")
    println("=" * 60)
    
    // 整体盈利指标
    val totalSales = coffeeDataRDD.map(_.coffeeSales).sum()
    val totalProfit = coffeeDataRDD.map(_.profit).sum()
    val totalCogs = coffeeDataRDD.map(_.cogs).sum()
    val totalMarketing = coffeeDataRDD.map(_.marketing).sum()
    val totalExpenses = coffeeDataRDD.map(_.totalExpenses).sum()
    val recordCount = coffeeDataRDD.count()
    
    val avgSales = totalSales / recordCount
    val avgProfit = totalProfit / recordCount
    val avgCogs = totalCogs / recordCount
    val avgMarketing = totalMarketing / recordCount
    val avgExpenses = totalExpenses / recordCount
    
    val overallProfitMargin = (totalProfit / totalSales * 100).formatted("%.2f")
    val roi = (totalProfit / totalExpenses * 100).formatted("%.2f")
    
    println("整体盈利指标：")
    println(f"总销售额：${totalSales.formatted("%.2f")}")
    println(f"总利润：${totalProfit.formatted("%.2f")}")
    println(f"总成本：${totalCogs.formatted("%.2f")}")
    println(f"总营销费用：${totalMarketing.formatted("%.2f")}")
    println(f"总费用：${totalExpenses.formatted("%.2f")}")
    println(f"整体利润率：$overallProfitMargin%")
    println(f"投资回报率(ROI)：$roi%")
    
    println(f"\n平均指标：")
    println(f"平均销售额：${avgSales.formatted("%.2f")}")
    println(f"平均利润：${avgProfit.formatted("%.2f")}")
    println(f"平均成本：${avgCogs.formatted("%.2f")}")
    println(f"平均营销费用：${avgMarketing.formatted("%.2f")}")
    println(f"平均总费用：${avgExpenses.formatted("%.2f")}")
    
    // 产品盈利能力排名
    val productProfitability = coffeeDataRDD
      .map(data => (data.product, (data.coffeeSales, data.profit, data.cogs, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4))
      .map { case (product, (sales, profit, cogs, count)) =>
        val profitMargin = if (sales > 0) profit / sales * 100 else 0
        val avgProfit = profit / count
        (product, sales, profit, profitMargin, avgProfit)
      }
      .sortBy(_._4, ascending = false)  // 按利润率排序
      .take(10)
    
    println(f"\n产品盈利能力排名（Top 10）：")
    println("产品名称\t\t\t总销售额\t总利润\t\t利润率\t\t平均利润")
    productProfitability.foreach { case (product, sales, profit, margin, avgProfit) =>
      println(f"$product%-25s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${margin.formatted("%.2f")}%\t\t${avgProfit.formatted("%.2f")}")
    }
  }

  /**
   * 产品性能分析
   */
  def performProductPerformanceAnalysis(coffeeDataRDD: RDD[CoffeeSalesData]): Unit = {
    println("\n" + "=" * 60)
    println("5. 产品性能分析")
    println("=" * 60)

    // 产品类型分析
    val productTypeAnalysis = coffeeDataRDD
      .map(data => (data.productType, (data.coffeeSales, data.profit, data.inventory, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4))
      .map { case (productType, (sales, profit, inventory, count)) =>
        val avgSales = sales / count
        val avgProfit = profit / count
        val avgInventory = inventory / count
        val profitMargin = if (sales > 0) profit / sales * 100 else 0
        (productType, sales, profit, avgSales, avgProfit, avgInventory, profitMargin, count)
      }
      .sortBy(_._2, ascending = false)
      .collect()

    println("产品类型性能分析：")
    println("产品类型\t总销售额\t总利润\t\t平均销售额\t平均利润\t平均库存\t利润率\t\t记录数")
    productTypeAnalysis.foreach { case (productType, sales, profit, avgSales, avgProfit, avgInventory, margin, count) =>
      println(f"$productType%-12s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${avgInventory.formatted("%.2f")}\t${margin.formatted("%.2f")}%\t\t$count")
    }

    // 咖啡类型详细分析
    val coffeeTypeDetailAnalysis = coffeeDataRDD
      .map(data => (data.coffeeType, (data.coffeeSales, data.profit, data.cogs, data.marketing, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4, a._5 + b._5))
      .map { case (coffeeType, (sales, profit, cogs, marketing, count)) =>
        val avgSales = sales / count
        val avgProfit = profit / count
        val avgCogs = cogs / count
        val avgMarketing = marketing / count
        val profitMargin = if (sales > 0) profit / sales * 100 else 0
        val costRatio = if (sales > 0) cogs / sales * 100 else 0
        (coffeeType, sales, profit, avgSales, avgProfit, avgCogs, avgMarketing, profitMargin, costRatio, count)
      }
      .sortBy(_._2, ascending = false)
      .collect()

    println(f"\n咖啡类型详细分析：")
    println("咖啡类型\t总销售额\t总利润\t\t平均销售额\t平均利润\t平均成本\t平均营销\t利润率\t\t成本率\t\t记录数")
    coffeeTypeDetailAnalysis.foreach { case (coffeeType, sales, profit, avgSales, avgProfit, avgCogs, avgMarketing, profitMargin, costRatio, count) =>
      println(f"$coffeeType%-8s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${avgCogs.formatted("%.2f")}\t${avgMarketing.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t${costRatio.formatted("%.2f")}%\t\t$count")
    }
  }

  /**
   * 区域分析
   */
  def performRegionalAnalysis(coffeeDataRDD: RDD[CoffeeSalesData]): Unit = {
    println("\n" + "=" * 60)
    println("6. 区域分析")
    println("=" * 60)

    // 州级详细分析
    val stateDetailAnalysis = coffeeDataRDD
      .map(data => (data.state, (data.coffeeSales, data.profit, data.cogs, data.marketing, data.inventory, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4, a._5 + b._5, a._6 + b._6))
      .map { case (state, (sales, profit, cogs, marketing, inventory, count)) =>
        val avgSales = sales / count
        val avgProfit = profit / count
        val avgCogs = cogs / count
        val avgMarketing = marketing / count
        val avgInventory = inventory / count
        val profitMargin = if (sales > 0) profit / sales * 100 else 0
        val efficiency = if (marketing > 0) sales / marketing else 0
        (state, sales, profit, avgSales, avgProfit, avgCogs, avgMarketing, avgInventory, profitMargin, efficiency, count)
      }
      .sortBy(_._2, ascending = false)
      .collect()

    println("各州详细业务分析：")
    println("州名\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t平均成本\t平均营销\t平均库存\t利润率\t\t营销效率\t记录数")
    stateDetailAnalysis.foreach { case (state, sales, profit, avgSales, avgProfit, avgCogs, avgMarketing, avgInventory, profitMargin, efficiency, count) =>
      println(f"$state%-12s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${avgCogs.formatted("%.2f")}\t${avgMarketing.formatted("%.2f")}\t${avgInventory.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t${efficiency.formatted("%.2f")}\t\t$count")
    }

    // 区域代码分析
    val areaCodeAnalysis = coffeeDataRDD
      .map(data => (data.areaCode, (data.coffeeSales, data.profit, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case (areaCode, (sales, profit, count)) =>
        val avgSales = sales / count
        val avgProfit = profit / count
        val profitMargin = if (sales > 0) profit / sales * 100 else 0
        (areaCode, sales, profit, avgSales, avgProfit, profitMargin, count)
      }
      .sortBy(_._2, ascending = false)
      .take(10)

    println(f"\n区域代码业绩排名（Top 10）：")
    println("区域代码\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
    areaCodeAnalysis.foreach { case (areaCode, sales, profit, avgSales, avgProfit, profitMargin, count) =>
      println(f"$areaCode%-8s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t$count")
    }
  }

  /**
   * 时间序列分析
   */
  def performTimeSeriesAnalysis(coffeeDataRDD: RDD[CoffeeSalesData]): Unit = {
    println("\n" + "=" * 60)
    println("7. 时间序列分析")
    println("=" * 60)

    // 按日期分析（提取月份）
    val monthlyAnalysis = coffeeDataRDD
      .map { data =>
        val month = try {
          val dateParts = data.date.split("/")
          if (dateParts.length >= 2) s"${dateParts(0)}月" else "未知"
        } catch {
          case _: Exception => "未知"
        }
        (month, (data.coffeeSales, data.profit, 1))
      }
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case (month, (sales, profit, count)) =>
        val avgSales = sales / count
        val avgProfit = profit / count
        (month, sales, profit, avgSales, avgProfit, count)
      }
      .collect()
      .sortBy(_._1)

    println("月度销售趋势分析：")
    println("月份\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t记录数")
    monthlyAnalysis.foreach { case (month, sales, profit, avgSales, avgProfit, count) =>
      println(f"$month%-8s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t$count")
    }
  }

  /**
   * 业务洞察分析
   */
  def performBusinessInsights(coffeeDataRDD: RDD[CoffeeSalesData]): Unit = {
    println("\n" + "=" * 60)
    println("8. 业务洞察分析")
    println("=" * 60)

    // 高价值客户分析（基于销售额）
    val highValueTransactions = coffeeDataRDD
      .filter(_.coffeeSales > 100)  // 销售额超过100的交易
      .map(data => (data.state, data.product, data.coffeeSales, data.profit))
      .collect()
      .sortBy(_._3)(Ordering[Double].reverse)
      .take(10)

    println("高价值交易分析（销售额>100）：")
    println("州名\t\t产品\t\t\t\t销售额\t\t利润")
    highValueTransactions.foreach { case (state, product, sales, profit) =>
      println(f"$state%-12s\t$product%-25s\t${sales.formatted("%.2f")}\t\t${profit.formatted("%.2f")}")
    }

    // 成本效益分析
    val costEfficiencyAnalysis = coffeeDataRDD
      .map { data =>
        val efficiency = if (data.totalExpenses > 0) data.profit / data.totalExpenses else 0
        val salesEfficiency = if (data.marketing > 0) data.coffeeSales / data.marketing else 0
        (data.product, efficiency, salesEfficiency, data.profit, data.coffeeSales)
      }
      .groupBy(_._1)
      .map { case (product, records) =>
        val recordsList = records.toList
        val avgEfficiency = recordsList.map(_._2).sum / recordsList.length
        val avgSalesEfficiency = recordsList.map(_._3).sum / recordsList.length
        val totalProfit = recordsList.map(_._4).sum
        val totalSales = recordsList.map(_._5).sum
        (product, avgEfficiency, avgSalesEfficiency, totalProfit, totalSales, recordsList.length)
      }
      .toSeq
      .sortBy(_._2)(Ordering[Double].reverse)
      .take(10)

    println(f"\n成本效益最佳产品（Top 10）：")
    println("产品名称\t\t\t\t平均成本效益\t平均营销效益\t总利润\t\t总销售额\t记录数")
    costEfficiencyAnalysis.foreach { case (product, efficiency, salesEfficiency, totalProfit, totalSales, count) =>
      println(f"$product%-30s\t${efficiency.formatted("%.3f")}\t\t${salesEfficiency.formatted("%.2f")}\t\t${totalProfit.formatted("%.2f")}\t${totalSales.formatted("%.2f")}\t$count")
    }

    // 库存周转分析
    val inventoryTurnoverAnalysis = coffeeDataRDD
      .filter(_.inventory > 0)
      .map { data =>
        val turnover = data.coffeeSales / data.inventory
        (data.productType, turnover, data.inventory, data.coffeeSales)
      }
      .groupBy(_._1)
      .map { case (productType, records) =>
        val recordsList = records.toList
        val avgTurnover = recordsList.map(_._2).sum / recordsList.length
        val avgInventory = recordsList.map(_._3).sum / recordsList.length
        val totalSales = recordsList.map(_._4).sum
        (productType, avgTurnover, avgInventory, totalSales, recordsList.length)
      }
      .toSeq
      .sortBy(_._2)(Ordering[Double].reverse)

    println(f"\n库存周转率分析：")
    println("产品类型\t\t平均周转率\t平均库存\t总销售额\t记录数")
    inventoryTurnoverAnalysis.foreach { case (productType, turnover, inventory, sales, count) =>
      println(f"$productType%-15s\t${turnover.formatted("%.2f")}\t\t${inventory.formatted("%.2f")}\t${sales.formatted("%.2f")}\t$count")
    }
  }

  /**
   * 保存综合分析结果
   */
  def saveComprehensiveResults(coffeeDataRDD: RDD[CoffeeSalesData], sc: SparkContext): Unit = {
    println("\n正在保存综合分析结果...")

    val totalRecords = coffeeDataRDD.count()
    val totalSales = coffeeDataRDD.map(_.coffeeSales).sum()
    val totalProfit = coffeeDataRDD.map(_.profit).sum()
    val avgSales = totalSales / totalRecords
    val avgProfit = totalProfit / totalRecords
    val overallProfitMargin = (totalProfit / totalSales * 100).formatted("%.2f")

    val results = sc.parallelize(Seq(
      "=" * 80,
      "咖啡连锁店数据分析综合报告",
      "=" * 80,
      s"分析时间：${java.time.LocalDateTime.now()}",
      s"数据文件：/home/<USER>/CoffeeChain.csv",
      s"分析记录数：$totalRecords",
      "",
      "核心业务指标：",
      f"总销售额：${totalSales.formatted("%.2f")}",
      f"总利润：${totalProfit.formatted("%.2f")}",
      f"平均销售额：${avgSales.formatted("%.2f")}",
      f"平均利润：${avgProfit.formatted("%.2f")}",
      f"整体利润率：$overallProfitMargin%",
      "",
      "主要发现：",
      "1. 产品销售排名和市场表现已完成分析",
      "2. 各州和区域的业务分布已统计完成",
      "3. 盈利能力和成本效益分析已完成",
      "4. 产品性能和库存周转分析已完成",
      "5. 时间序列趋势分析已完成",
      "",
      "详细分析结果请查看控制台输出",
      "=" * 80
    ))

    val outputPath = "/home/<USER>/coffee_comprehensive_analysis"
    results.coalesce(1).saveAsTextFile(outputPath)
    println(s"✓ 综合分析结果已保存到：$outputPath")
  }
}
