#!/bin/bash

# 人口数据分析运行脚本
echo "开始人口数据分析..."

# 检查数据文件
if [ ! -f "peopleage.txt" ]; then
    echo "正在生成人口数据..."
    scala DataGenerator.scala
fi

# 使用spark-shell运行分析
echo "启动Spark分析..."
spark-shell --driver-memory 2g --executor-memory 2g << 'EOF'

// 读取数据文件
val rawData = sc.textFile("/home/<USER>/spark/peopleage.txt")
val totalRecords = rawData.count()

println(s"\n人口数据读取完成，记录数：$totalRecords")

// 显示数据示例
println("\n数据示例（前5条）：")
rawData.take(5).foreach(println)

// 解析数据：提取年龄字段
val agesRDD = rawData.map { line =>
  val parts = line.split("\t")
  parts(1).toInt  // 提取年龄（第二列）
}

// 缓存RDD以提高性能
agesRDD.cache()

// 基础年龄统计
println("\n=== 基础年龄统计分析 ===")

val totalAge = agesRDD.reduce(_ + _)
val ageCount = agesRDD.count()
val averageAge = totalAge.toDouble / ageCount
val maxAge = agesRDD.max()
val minAge = agesRDD.min()

// 计算中位数
val sortedAges = agesRDD.collect().sorted
val median = if (sortedAges.length % 2 == 0) {
  (sortedAges(sortedAges.length / 2 - 1) + sortedAges(sortedAges.length / 2)) / 2.0
} else {
  sortedAges(sortedAges.length / 2).toDouble
}

// 计算方差和标准差
val mean = averageAge
val variance = agesRDD.map(age => math.pow(age - mean, 2)).mean()
val stdDev = math.sqrt(variance)

println(f"总人数：$ageCount")
println(f"年龄总和：$totalAge")
println(f"平均年龄：$averageAge%.2f 岁")
println(f"年龄中位数：$median%.1f 岁")
println(f"最大年龄：$maxAge 岁")
println(f"最小年龄：$minAge 岁")
println(f"年龄跨度：${maxAge - minAge} 岁")
println(f"标准差：$stdDev%.2f 岁")
println(f"方差：$variance%.2f")

// 年龄段分布分析
println("\n=== 年龄段分布分析 ===")

val ageGroups = agesRDD.map { age =>
  val ageGroup = age match {
    case a if a < 18 => "未成年(0-17岁)"
    case a if a < 25 => "青年初期(18-24岁)"
    case a if a < 35 => "青年期(25-34岁)"
    case a if a < 45 => "中年初期(35-44岁)"
    case a if a < 55 => "中年期(45-54岁)"
    case a if a < 65 => "中年后期(55-64岁)"
    case _ => "老年期(65岁以上)"
  }
  (ageGroup, age)
}

val ageGroupStats = ageGroups
  .groupByKey()
  .map { case (group, ages) =>
    val ageList = ages.toList
    val count = ageList.length
    val avgAge = ageList.sum.toDouble / count
    (group, count, avgAge)
  }
  .collect()
  .sortBy(_._2)(Ordering[Int].reverse)

println("年龄段分布统计：")
println("年龄段\t\t\t人数\t比例\t\t平均年龄")
ageGroupStats.foreach { case (group, count, avgAge) =>
  val percentage = (count.toDouble / ageCount * 100).formatted("%.2f")
  println(f"$group%-15s\t$count\t$percentage%%-8s\t$avgAge%.2f岁")
}

// 保存分析结果
println("\n正在保存分析结果...")

val populationResults = sc.parallelize(Seq(
  "=" * 60,
  "人口年龄数据分析报告",
  "=" * 60,
  s"分析时间：${java.time.LocalDateTime.now()}",
  s"数据文件：/home/<USER>/spark/peopleage.txt",
  s"总记录数：$ageCount",
  "",
  "基础统计：",
  s"年龄总和：$totalAge",
  f"平均年龄：$averageAge%.2f 岁",
  f"年龄中位数：$median%.1f 岁",
  s"最大年龄：$maxAge 岁",
  s"最小年龄：$minAge 岁",
  s"年龄跨度：${maxAge - minAge} 岁",
  f"标准差：$stdDev%.2f 岁",
  f"方差：$variance%.2f",
  "",
  "年龄段分布：",
  ageGroupStats.map { case (group, count, avgAge) =>
    val percentage = (count.toDouble / ageCount * 100).formatted("%.2f")
    f"$group: $count 人 ($percentage%), 平均年龄 $avgAge%.2f岁"
  }.mkString("\n")
))

// 清理旧的输出目录
import java.io.File
def deleteDirectory(dir: File): Boolean = {
  if (dir.exists()) {
    val files = dir.listFiles()
    if (files != null) {
      files.foreach(deleteDirectory)
    }
    dir.delete()
  } else {
    true
  }
}

val outputPath = "/home/<USER>/spark/population_analysis_result"
val outputDir = new File(outputPath)
if (outputDir.exists()) {
  deleteDirectory(outputDir)
  println(s"✓ 已清理旧的输出目录：$outputPath")
}

populationResults.coalesce(1).saveAsTextFile(outputPath)
println(s"✓ 人口分析结果已保存到：$outputPath")

// 验证文件是否成功保存
val resultFile = new File(outputPath + "/part-00000")
if (resultFile.exists()) {
  println(s"✓ 结果文件已生成：${resultFile.getAbsolutePath()}")
  println(s"✓ 文件大小：${resultFile.length()} 字节")
} else {
  println(s"✗ 警告：结果文件未找到")
}

println("\n=== 人口数据分析完成 ===")

// 退出spark-shell
System.exit(0)

EOF

echo "人口数据分析完成！"
