#!/bin/bash

# 简化测试脚本 - 使用正确的方式运行Spark分析
echo "简化Spark分析测试..."

WORK_DIR="/home/<USER>/spark"
cd $WORK_DIR

echo "1. 清理旧结果..."
rm -rf population_analysis_result coffee_comprehensive_analysis
echo "✓ 旧结果已清理"
echo ""

echo "2. 检查和生成人口数据..."
if [ ! -f "peopleage.txt" ]; then
    echo "正在生成人口数据..."
    scala DataGenerator.scala
fi

if [ -f "peopleage.txt" ]; then
    echo "✓ 人口数据文件存在"
    echo "文件大小：$(wc -l < peopleage.txt) 行"
else
    echo "✗ 人口数据文件不存在"
    exit 1
fi
echo ""

echo "3. 运行人口数据分析..."
chmod +x RunPopulationAnalysis.sh
if ./RunPopulationAnalysis.sh; then
    echo "✓ 人口数据分析完成"
    
    if [ -d "population_analysis_result" ]; then
        echo "✓ 人口分析结果目录已生成"
        if [ -f "population_analysis_result/part-00000" ]; then
            echo "✓ 人口分析结果文件存在"
            echo "文件大小：$(wc -l < population_analysis_result/part-00000) 行"
            echo ""
            echo "人口分析结果内容："
            echo "===================="
            cat population_analysis_result/part-00000
            echo "===================="
        else
            echo "✗ 人口分析结果文件不存在"
        fi
    else
        echo "✗ 人口分析结果目录未生成"
    fi
else
    echo "✗ 人口数据分析失败"
fi
echo ""

echo "4. 检查咖啡数据文件..."
if [ -f "CoffeeChain.csv" ]; then
    echo "✓ 咖啡数据文件存在"
    echo "文件大小：$(wc -l < CoffeeChain.csv) 行"
    
    echo ""
    echo "5. 运行咖啡数据分析..."
    chmod +x RunCoffeeAnalysis.sh
    if ./RunCoffeeAnalysis.sh; then
        echo "✓ 咖啡数据分析完成"
        
        if [ -d "coffee_comprehensive_analysis" ]; then
            echo "✓ 咖啡分析结果目录已生成"
            if [ -f "coffee_comprehensive_analysis/part-00000" ]; then
                echo "✓ 咖啡分析结果文件存在"
                echo "文件大小：$(wc -l < coffee_comprehensive_analysis/part-00000) 行"
                echo ""
                echo "咖啡分析结果内容（前20行）："
                echo "================================"
                head -20 coffee_comprehensive_analysis/part-00000
                echo "================================"
            else
                echo "✗ 咖啡分析结果文件不存在"
            fi
        else
            echo "✗ 咖啡分析结果目录未生成"
        fi
    else
        echo "✗ 咖啡数据分析失败"
    fi
else
    echo "✗ 咖啡数据文件不存在，跳过咖啡分析"
fi
echo ""

echo "6. 最终结果检查..."
echo "当前目录内容："
ls -la
echo ""

echo "分析结果文件："
for dir in population_analysis_result coffee_comprehensive_analysis; do
    if [ -d "$dir" ]; then
        echo "✓ $dir 存在"
        if [ -f "$dir/part-00000" ]; then
            echo "  ✓ 结果文件存在，大小：$(du -h $dir/part-00000 | cut -f1)"
        else
            echo "  ✗ 结果文件不存在"
        fi
    else
        echo "✗ $dir 不存在"
    fi
done

echo ""
echo "测试完成！"
echo ""
echo "如果看到 ✓ 表示成功，✗ 表示失败"
