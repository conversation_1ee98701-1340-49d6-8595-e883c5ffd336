import org.apache.spark.{SparkContext, SparkConf}
import org.apache.spark.rdd.RDD

/**
 * 人口年龄数据分析器
 * 使用Spark RDD计算人口平均年龄
 * 数据格式：序号    年龄
 * 工作目录：/home/<USER>/spark/
 */
object PopulationAnalyzer {
  
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("Population Age Analyzer")
      .setMaster("local[*]")
      .set("spark.sql.adaptive.enabled", "true")
      .set("spark.sql.adaptive.coalescePartitions.enabled", "true")
    
    val sc = new SparkContext(conf)
    
    try {
      println("=" * 70)
      println("Spark RDD 人口年龄数据分析系统")
      println("=" * 70)
      
      // 读取数据文件
      val inputFile = "/home/<USER>/spark/peopleage.txt"
      println(s"正在读取数据文件：$inputFile")

      val rawData = sc.textFile(inputFile)
      val totalRecords = rawData.count()
      println(s"原始数据记录数：$totalRecords")

      // 显示数据示例
      println("\n数据示例（前5条）：")
      rawData.take(5).foreach(println)

      // 解析数据：提取年龄字段
      val agesRDD = rawData.map { line =>
        val parts = line.split("\t")
        if (parts.length >= 2) {
          parts(1).toInt  // 提取年龄（第二列）
        } else {
          throw new IllegalArgumentException(s"数据格式错误：$line")
        }
      }

      // 缓存RDD以提高性能
      agesRDD.cache()
      
      // 执行年龄统计分析
      performBasicAgeAnalysis(agesRDD)
      performAgeGroupAnalysis(agesRDD)

      // 保存分析结果
      saveAnalysisResults(agesRDD, totalRecords, sc)
      
      println("\n" + "=" * 70)
      println("✓ 所有分析完成！")
      println("=" * 70)
      
    } catch {
      case e: Exception =>
        println(s"✗ 程序执行出错：${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
  
  /**
   * 基础年龄统计分析
   */
  def performBasicAgeAnalysis(agesRDD: RDD[Int]): Unit = {
    println("\n" + "=" * 50)
    println("1. 基础年龄统计分析")
    println("=" * 50)

    val totalAge = agesRDD.reduce(_ + _)
    val count = agesRDD.count()
    val averageAge = totalAge.toDouble / count
    val maxAge = agesRDD.max()
    val minAge = agesRDD.min()

    // 计算中位数
    val sortedAges = agesRDD.collect().sorted
    val median = if (sortedAges.length % 2 == 0) {
      (sortedAges(sortedAges.length / 2 - 1) + sortedAges(sortedAges.length / 2)) / 2.0
    } else {
      sortedAges(sortedAges.length / 2).toDouble
    }

    // 计算方差和标准差
    val mean = averageAge
    val variance = agesRDD.map(age => math.pow(age - mean, 2)).mean()
    val stdDev = math.sqrt(variance)

    println(f"总人数：$count")
    println(f"年龄总和：$totalAge")
    println(f"平均年龄：$averageAge%.2f 岁")
    println(f"年龄中位数：$median%.1f 岁")
    println(f"最大年龄：$maxAge 岁")
    println(f"最小年龄：$minAge 岁")
    println(f"年龄跨度：${maxAge - minAge} 岁")
    println(f"标准差：$stdDev%.2f 岁")
    println(f"方差：$variance%.2f")
  }
  
  /**
   * 年龄段分布分析
   */
  def performAgeGroupAnalysis(agesRDD: RDD[Int]): Unit = {
    println("\n" + "=" * 50)
    println("2. 年龄段分布分析")
    println("=" * 50)

    val ageGroups = agesRDD.map { age =>
      val ageGroup = age match {
        case a if a < 18 => "未成年(0-17岁)"
        case a if a < 25 => "青年初期(18-24岁)"
        case a if a < 35 => "青年期(25-34岁)"
        case a if a < 45 => "中年初期(35-44岁)"
        case a if a < 55 => "中年期(45-54岁)"
        case a if a < 65 => "中年后期(55-64岁)"
        case _ => "老年期(65岁以上)"
      }
      (ageGroup, age)
    }

    val ageGroupStats = ageGroups
      .groupByKey()
      .map { case (group, ages) =>
        val ageList = ages.toList
        val count = ageList.length
        val avgAge = ageList.sum.toDouble / count
        (group, count, avgAge)
      }
      .collect()
      .sortBy(_._2)(Ordering[Int].reverse)  // 按人数降序

    val total = agesRDD.count()
    println("年龄段分布统计：")
    println("年龄段\t\t\t人数\t比例\t\t平均年龄")
    ageGroupStats.foreach { case (group, count, avgAge) =>
      val percentage = (count.toDouble / total * 100).formatted("%.2f")
      println(f"$group%-15s\t$count\t$percentage%%-8s\t$avgAge%.2f岁")
    }
  }
  
  /**
   * 保存分析结果到文件
   */
  def saveAnalysisResults(agesRDD: RDD[Int], totalRecords: Long, sc: SparkContext): Unit = {
    println("\n正在保存分析结果...")

    val totalAge = agesRDD.reduce(_ + _)
    val averageAge = totalAge.toDouble / totalRecords
    val maxAge = agesRDD.max()
    val minAge = agesRDD.min()

    val results = sc.parallelize(Seq(
      "=" * 60,
      "人口年龄数据分析报告",
      "=" * 60,
      s"分析时间：${java.time.LocalDateTime.now()}",
      s"数据文件：/home/<USER>/spark/peopleage.txt",
      s"总记录数：$totalRecords",
      "",
      "基础统计：",
      s"年龄总和：$totalAge",
      f"平均年龄：$averageAge%.2f 岁",
      s"最大年龄：$maxAge 岁",
      s"最小年龄：$minAge 岁",
      s"年龄跨度：${maxAge - minAge} 岁",
      "",
      "详细分析结果请查看控制台输出",
      "=" * 60
    ))

    val outputPath = "/home/<USER>/spark/population_analysis_result"
    results.coalesce(1).saveAsTextFile(outputPath)
    println(s"✓ 分析结果已保存到：$outputPath")
  }
}
