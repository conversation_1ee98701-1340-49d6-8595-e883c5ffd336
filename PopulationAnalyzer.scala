import org.apache.spark.{SparkContext, SparkConf}
import org.apache.spark.rdd.RDD

/**
 * 人口年龄数据分析器
 * 使用Spark RDD进行大数据处理和统计分析
 * 工作目录：/home/<USER>
 */
object PopulationAnalyzer {
  
  // 定义人口数据的case class
  case class PersonData(
    id: String,
    name: String,
    age: Int,
    gender: String,
    city: String
  )
  
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("Population Age Analyzer")
      .setMaster("local[*]")
      .set("spark.sql.adaptive.enabled", "true")
      .set("spark.sql.adaptive.coalescePartitions.enabled", "true")
    
    val sc = new SparkContext(conf)
    
    try {
      println("=" * 70)
      println("Spark RDD 人口年龄数据分析系统")
      println("=" * 70)
      
      // 读取数据文件
      val inputFile = "/home/<USER>/population_data.txt"
      println(s"正在读取数据文件：$inputFile")
      
      val rawData = sc.textFile(inputFile)
      val header = rawData.first()
      val dataLines = rawData.filter(_ != header)
      
      println(s"数据表头：$header")
      println(s"原始数据记录数：${dataLines.count()}")
      
      // 解析数据
      val personRDD = dataLines.map(parsePerson).filter(_.isDefined).map(_.get)
      personRDD.cache()  // 缓存数据以提高性能
      
      val validRecords = personRDD.count()
      println(s"有效数据记录数：$validRecords")
      
      // 显示数据示例
      println("\n数据解析示例（前5条）：")
      personRDD.take(5).foreach(person => 
        println(s"${person.id} | ${person.name} | ${person.age}岁 | ${person.gender} | ${person.city}")
      )
      
      // 执行各种分析
      performBasicAgeAnalysis(personRDD)
      performGenderAnalysis(personRDD)
      performCityAnalysis(personRDD)
      performAgeGroupAnalysis(personRDD)
      performAdvancedAnalysis(personRDD)
      
      // 保存分析结果
      saveAnalysisResults(personRDD, sc)
      
      println("\n" + "=" * 70)
      println("✓ 所有分析完成！")
      println("=" * 70)
      
    } catch {
      case e: Exception =>
        println(s"✗ 程序执行出错：${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
  
  /**
   * 解析CSV行数据为PersonData对象
   */
  def parsePerson(line: String): Option[PersonData] = {
    try {
      val parts = line.split(",")
      if (parts.length >= 5) {
        Some(PersonData(
          id = parts(0),
          name = parts(1),
          age = parts(2).toInt,
          gender = parts(3),
          city = parts(4)
        ))
      } else None
    } catch {
      case _: Exception => None
    }
  }
  
  /**
   * 基础年龄统计分析
   */
  def performBasicAgeAnalysis(personRDD: RDD[PersonData]): Unit = {
    println("\n" + "=" * 50)
    println("1. 基础年龄统计分析")
    println("=" * 50)
    
    val ages = personRDD.map(_.age)
    val totalAge = ages.reduce(_ + _)
    val count = ages.count()
    val averageAge = totalAge.toDouble / count
    val maxAge = ages.max()
    val minAge = ages.min()
    
    // 计算中位数
    val sortedAges = ages.collect().sorted
    val median = if (sortedAges.length % 2 == 0) {
      (sortedAges(sortedAges.length / 2 - 1) + sortedAges(sortedAges.length / 2)) / 2.0
    } else {
      sortedAges(sortedAges.length / 2).toDouble
    }
    
    println(f"总人数：$count")
    println(f"年龄总和：$totalAge")
    println(f"平均年龄：$averageAge%.2f 岁")
    println(f"年龄中位数：$median%.1f 岁")
    println(f"最大年龄：$maxAge 岁")
    println(f"最小年龄：$minAge 岁")
    println(f"年龄跨度：${maxAge - minAge} 岁")
  }
  
  /**
   * 性别分布分析
   */
  def performGenderAnalysis(personRDD: RDD[PersonData]): Unit = {
    println("\n" + "=" * 50)
    println("2. 性别分布分析")
    println("=" * 50)
    
    val genderCounts = personRDD.map(_.gender).countByValue()
    val total = personRDD.count()
    
    println("性别分布统计：")
    genderCounts.foreach { case (gender, count) =>
      val percentage = (count.toDouble / total * 100).formatted("%.2f")
      println(f"$gender: $count 人 ($percentage%)")
    }
    
    // 按性别统计平均年龄
    val genderAgeStats = personRDD
      .map(person => (person.gender, person.age))
      .groupByKey()
      .map { case (gender, ages) =>
        val ageList = ages.toList
        val avgAge = ageList.sum.toDouble / ageList.length
        val maxAge = ageList.max
        val minAge = ageList.min
        (gender, avgAge, maxAge, minAge, ageList.length)
      }
      .collect()
    
    println("\n各性别年龄统计：")
    println("性别\t平均年龄\t最大年龄\t最小年龄\t人数")
    genderAgeStats.foreach { case (gender, avg, max, min, count) =>
      println(f"$gender\t$avg%.2f岁\t\t$max岁\t\t$min岁\t\t$count")
    }
  }
  
  /**
   * 城市分布分析
   */
  def performCityAnalysis(personRDD: RDD[PersonData]): Unit = {
    println("\n" + "=" * 50)
    println("3. 城市分布分析")
    println("=" * 50)
    
    val cityStats = personRDD
      .map(person => (person.city, person.age))
      .groupByKey()
      .map { case (city, ages) =>
        val ageList = ages.toList
        val total = ageList.length
        val avgAge = ageList.sum.toDouble / total
        val maxAge = ageList.max
        val minAge = ageList.min
        (city, total, avgAge, maxAge, minAge)
      }
      .sortBy(_._2, ascending = false)  // 按人数降序排列
      .collect()
    
    println("各城市人口统计：")
    println("城市\t\t人数\t平均年龄\t最大年龄\t最小年龄")
    cityStats.foreach { case (city, count, avg, max, min) =>
      println(f"$city%-8s\t$count\t$avg%.2f岁\t\t$max岁\t\t$min岁")
    }
    
    val totalPeople = personRDD.count()
    println(f"\n城市分布比例（Top 5）：")
    cityStats.take(5).foreach { case (city, count, _, _, _) =>
      val percentage = (count.toDouble / totalPeople * 100).formatted("%.2f")
      println(f"$city: $count 人 ($percentage%)")
    }
  }
  
  /**
   * 年龄段分布分析
   */
  def performAgeGroupAnalysis(personRDD: RDD[PersonData]): Unit = {
    println("\n" + "=" * 50)
    println("4. 年龄段分布分析")
    println("=" * 50)
    
    val ageGroups = personRDD.map { person =>
      val ageGroup = person.age match {
        case age if age < 18 => "未成年(0-17岁)"
        case age if age < 25 => "青年初期(18-24岁)"
        case age if age < 35 => "青年期(25-34岁)"
        case age if age < 45 => "中年初期(35-44岁)"
        case age if age < 55 => "中年期(45-54岁)"
        case age if age < 65 => "中年后期(55-64岁)"
        case _ => "老年期(65岁以上)"
      }
      (ageGroup, person.age)
    }
    
    val ageGroupStats = ageGroups
      .groupByKey()
      .map { case (group, ages) =>
        val ageList = ages.toList
        val count = ageList.length
        val avgAge = ageList.sum.toDouble / count
        (group, count, avgAge)
      }
      .collect()
      .sortBy(_._2)(Ordering[Int].reverse)  // 按人数降序
    
    val total = personRDD.count()
    println("年龄段分布统计：")
    println("年龄段\t\t\t人数\t比例\t\t平均年龄")
    ageGroupStats.foreach { case (group, count, avgAge) =>
      val percentage = (count.toDouble / total * 100).formatted("%.2f")
      println(f"$group%-15s\t$count\t$percentage%%-8s\t$avgAge%.2f岁")
    }
  }
  
  /**
   * 高级分析
   */
  def performAdvancedAnalysis(personRDD: RDD[PersonData]): Unit = {
    println("\n" + "=" * 50)
    println("5. 高级统计分析")
    println("=" * 50)
    
    // 计算年龄方差和标准差
    val ages = personRDD.map(_.age.toDouble)
    val mean = ages.mean()
    val variance = ages.map(age => math.pow(age - mean, 2)).mean()
    val stdDev = math.sqrt(variance)
    
    println(f"年龄统计指标：")
    println(f"平均值：$mean%.2f 岁")
    println(f"方差：$variance%.2f")
    println(f"标准差：$stdDev%.2f 岁")
    
    // 找出年龄最大和最小的人
    val oldestPerson = personRDD.reduce((p1, p2) => if (p1.age > p2.age) p1 else p2)
    val youngestPerson = personRDD.reduce((p1, p2) => if (p1.age < p2.age) p1 else p2)
    
    println(f"\n极值记录：")
    println(f"年龄最大：${oldestPerson.name}(${oldestPerson.id}) - ${oldestPerson.age}岁 - ${oldestPerson.city}")
    println(f"年龄最小：${youngestPerson.name}(${youngestPerson.id}) - ${youngestPerson.age}岁 - ${youngestPerson.city}")
    
    // 各城市性别比例分析
    val cityGenderStats = personRDD
      .map(person => ((person.city, person.gender), 1))
      .reduceByKey(_ + _)
      .map { case ((city, gender), count) => (city, (gender, count)) }
      .groupByKey()
      .collect()
    
    println(f"\n主要城市性别比例：")
    cityGenderStats.take(5).foreach { case (city, genderCounts) =>
      val genderMap = genderCounts.toMap
      val maleCount = genderMap.getOrElse("男", 0)
      val femaleCount = genderMap.getOrElse("女", 0)
      val total = maleCount + femaleCount
      if (total > 0) {
        val maleRatio = (maleCount.toDouble / total * 100).formatted("%.1f")
        val femaleRatio = (femaleCount.toDouble / total * 100).formatted("%.1f")
        println(f"$city: 男性$maleRatio% ($maleCount人), 女性$femaleRatio% ($femaleCount人)")
      }
    }
  }
  
  /**
   * 保存分析结果到文件
   */
  def saveAnalysisResults(personRDD: RDD[PersonData], sc: SparkContext): Unit = {
    println("\n正在保存分析结果...")
    
    val ages = personRDD.map(_.age)
    val averageAge = ages.mean()
    val maxAge = ages.max()
    val minAge = ages.min()
    val totalCount = personRDD.count()
    
    val results = sc.parallelize(Seq(
      "=" * 60,
      "人口年龄数据分析报告",
      "=" * 60,
      s"分析时间：${java.time.LocalDateTime.now()}",
      s"数据文件：/home/<USER>/population_data.txt",
      s"总记录数：$totalCount",
      "",
      "基础统计：",
      f"平均年龄：$averageAge%.2f 岁",
      s"最大年龄：$maxAge 岁",
      s"最小年龄：$minAge 岁",
      s"年龄跨度：${maxAge - minAge} 岁",
      "",
      "详细分析结果请查看控制台输出",
      "=" * 60
    ))
    
    val outputPath = "/home/<USER>/population_analysis_result"
    results.coalesce(1).saveAsTextFile(outputPath)
    println(s"✓ 分析结果已保存到：$outputPath")
  }
}
