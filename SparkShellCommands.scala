// Spark Shell 交互式分析代码
// 在spark-shell中逐段复制粘贴运行
// 工作目录：/home/<USER>

println("=" * 80)
println("Spark Shell 交互式数据分析系统")
println("工作目录：/home/<USER>")
println("=" * 80)

// ===== 第一部分：人口数据生成和分析 =====
println("\n=== 第一部分：人口数据生成和分析 ===")

// 1. 生成人口数据
import scala.util.Random
import java.io.{PrintWriter, File}

def generatePopulationData(): Unit = {
  val recordCount = 1500
  val minAge = 16
  val maxAge = 85
  val outputPath = "/home/<USER>/population_data.txt"
  
  val firstNames = Array("张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴")
  val secondNames = Array("伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军")
  val cities = Array("北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都")
  val genders = Array("男", "女")
  
  println(s"正在生成 $recordCount 条人口数据...")
  
  val random = new Random()
  val writer = new PrintWriter(new File(outputPath))
  
  try {
    writer.println("ID,姓名,年龄,性别,城市")
    
    for (i <- 1 to recordCount) {
      val id = f"P$i%06d"
      val firstName = firstNames(random.nextInt(firstNames.length))
      val secondName = secondNames(random.nextInt(secondNames.length))
      val name = firstName + secondName
      val age = minAge + random.nextInt(maxAge - minAge + 1)
      val gender = genders(random.nextInt(genders.length))
      val city = cities(random.nextInt(cities.length))
      
      writer.println(s"$id,$name,$age,$gender,$city")
      
      if (i % 300 == 0) {
        println(s"已生成 $i 条记录...")
      }
    }
    
    println(s"✓ 人口数据生成完成：$outputPath")
    
  } finally {
    writer.close()
  }
}

// 执行数据生成
generatePopulationData()

// 2. 读取和分析人口数据
val populationRawData = sc.textFile("/home/<USER>/population_data.txt")
val populationHeader = populationRawData.first()
val populationDataLines = populationRawData.filter(_ != populationHeader)

println(s"\n人口数据读取完成，记录数：${populationDataLines.count()}")
println(s"数据表头：$populationHeader")

// 定义人口数据结构
case class PersonInfo(id: String, name: String, age: Int, gender: String, city: String)

def parsePersonData(line: String): Option[PersonInfo] = {
  try {
    val parts = line.split(",")
    if (parts.length >= 5) {
      Some(PersonInfo(parts(0), parts(1), parts(2).toInt, parts(3), parts(4)))
    } else None
  } catch {
    case _: Exception => None
  }
}

val personRDD = populationDataLines.map(parsePersonData).filter(_.isDefined).map(_.get)
personRDD.cache()

val validPersonRecords = personRDD.count()
println(s"有效人口记录数：$validPersonRecords")

// 显示数据示例
println("\n人口数据示例（前5条）：")
personRDD.take(5).foreach(person => 
  println(s"${person.id} | ${person.name} | ${person.age}岁 | ${person.gender} | ${person.city}")
)

// 3. 基础年龄统计
println("\n=== 基础年龄统计分析 ===")

val ages = personRDD.map(_.age)
val totalAge = ages.reduce(_ + _)
val ageCount = ages.count()
val averageAge = totalAge.toDouble / ageCount
val maxAge = ages.max()
val minAge = ages.min()

println(f"总人数：$ageCount")
println(f"年龄总和：$totalAge")
println(f"平均年龄：$averageAge%.2f 岁")
println(f"最大年龄：$maxAge 岁")
println(f"最小年龄：$minAge 岁")
println(f"年龄跨度：${maxAge - minAge} 岁")

// 4. 性别分布分析
println("\n=== 性别分布分析 ===")

val genderCounts = personRDD.map(_.gender).countByValue()
val totalPeople = personRDD.count()

println("性别分布统计：")
genderCounts.foreach { case (gender, count) =>
  val percentage = (count.toDouble / totalPeople * 100).formatted("%.2f")
  println(f"$gender: $count 人 ($percentage%)")
}

// 按性别统计平均年龄
val genderAgeStats = personRDD
  .map(person => (person.gender, person.age))
  .groupByKey()
  .map { case (gender, ages) =>
    val ageList = ages.toList
    val avgAge = ageList.sum.toDouble / ageList.length
    val maxAge = ageList.max
    val minAge = ageList.min
    (gender, avgAge, maxAge, minAge, ageList.length)
  }
  .collect()

println("\n各性别年龄统计：")
println("性别\t平均年龄\t最大年龄\t最小年龄\t人数")
genderAgeStats.foreach { case (gender, avg, max, min, count) =>
  println(f"$gender\t$avg%.2f岁\t\t$max岁\t\t$min岁\t\t$count")
}

// 5. 城市分布分析
println("\n=== 城市分布分析 ===")

val cityStats = personRDD
  .map(person => (person.city, person.age))
  .groupByKey()
  .map { case (city, ages) =>
    val ageList = ages.toList
    val total = ageList.length
    val avgAge = ageList.sum.toDouble / total
    val maxAge = ageList.max
    val minAge = ageList.min
    (city, total, avgAge, maxAge, minAge)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("各城市人口统计：")
println("城市\t\t人数\t平均年龄\t最大年龄\t最小年龄")
cityStats.foreach { case (city, count, avg, max, min) =>
  println(f"$city%-8s\t$count\t$avg%.2f岁\t\t$max岁\t\t$min岁")
}

// 6. 年龄段分布分析
println("\n=== 年龄段分布分析 ===")

val ageGroups = personRDD.map { person =>
  val ageGroup = person.age match {
    case age if age < 18 => "未成年(0-17岁)"
    case age if age < 25 => "青年初期(18-24岁)"
    case age if age < 35 => "青年期(25-34岁)"
    case age if age < 45 => "中年初期(35-44岁)"
    case age if age < 55 => "中年期(45-54岁)"
    case age if age < 65 => "中年后期(55-64岁)"
    case _ => "老年期(65岁以上)"
  }
  (ageGroup, person.age)
}

val ageGroupStats = ageGroups
  .groupByKey()
  .map { case (group, ages) =>
    val ageList = ages.toList
    val count = ageList.length
    val avgAge = ageList.sum.toDouble / count
    (group, count, avgAge)
  }
  .collect()
  .sortBy(_._2)(Ordering[Int].reverse)

println("年龄段分布统计：")
println("年龄段\t\t\t人数\t比例\t\t平均年龄")
ageGroupStats.foreach { case (group, count, avgAge) =>
  val percentage = (count.toDouble / totalPeople * 100).formatted("%.2f")
  println(f"$group%-15s\t$count\t$percentage%%-8s\t$avgAge%.2f岁")
}

// 7. 保存人口分析结果
println("\n正在保存人口分析结果...")

val populationResults = sc.parallelize(Seq(
  "=" * 60,
  "人口年龄数据分析报告",
  "=" * 60,
  s"分析时间：${java.time.LocalDateTime.now()}",
  s"数据文件：/home/<USER>/population_data.txt",
  s"总记录数：$validPersonRecords",
  "",
  "基础统计：",
  f"平均年龄：$averageAge%.2f 岁",
  s"最大年龄：$maxAge 岁",
  s"最小年龄：$minAge 岁",
  s"年龄跨度：${maxAge - minAge} 岁",
  "",
  "性别分布：",
  genderCounts.map { case (gender, count) =>
    val percentage = (count.toDouble / totalPeople * 100).formatted("%.2f")
    f"$gender: $count 人 ($percentage%)"
  }.mkString("\n"),
  "",
  "城市分布（Top 5）：",
  cityStats.take(5).map { case (city, count, avg, _, _) =>
    val percentage = (count.toDouble / totalPeople * 100).formatted("%.2f")
    f"$city: $count 人 ($percentage%), 平均年龄 $avg%.2f岁"
  }.mkString("\n"),
  "",
  "年龄段分布：",
  ageGroupStats.map { case (group, count, avgAge) =>
    val percentage = (count.toDouble / totalPeople * 100).formatted("%.2f")
    f"$group: $count 人 ($percentage%), 平均年龄 $avgAge%.2f岁"
  }.mkString("\n")
))

populationResults.coalesce(1).saveAsTextFile("/home/<USER>/population_analysis_results")
println("✓ 人口分析结果已保存到：/home/<USER>/population_analysis_results")

println("\n=== 第一部分完成 ===")

// ===== 第二部分：咖啡数据分析 =====
println("\n=== 第二部分：咖啡数据分析 ===")

// 1. 读取咖啡数据
val coffeeRawData = sc.textFile("/home/<USER>/CoffeeChain.csv")
val coffeeHeader = coffeeRawData.first()
val coffeeDataLines = coffeeRawData.filter(_ != coffeeHeader)

println(s"咖啡数据读取完成，记录数：${coffeeDataLines.count()}")
println("数据表头：")
println(coffeeHeader)

// 定义简化的咖啡数据结构
case class CoffeeRecord(
  state: String,
  market: String,
  product: String,
  productType: String,
  coffeeType: String,
  coffeeSales: Double,
  profit: Double,
  margin: Double,
  cogs: Double,
  marketing: Double
)

def parseCoffeeRecord(line: String): Option[CoffeeRecord] = {
  try {
    val parts = line.split(",")
    if (parts.length >= 20) {
      Some(CoffeeRecord(
        state = parts(6),
        market = parts(2),
        product = parts(4),
        productType = parts(5),
        coffeeType = parts(7),
        coffeeSales = parts(12).replaceAll("\"", "").replaceAll(",", "").toDouble,
        profit = parts(18).toDouble,
        margin = parts(15).toDouble,
        cogs = parts(13).toDouble,
        marketing = parts(16).toDouble
      ))
    } else None
  } catch {
    case _: Exception => None
  }
}

val coffeeRDD = coffeeDataLines.map(parseCoffeeRecord).filter(_.isDefined).map(_.get)
coffeeRDD.cache()

val validCoffeeRecords = coffeeRDD.count()
println(s"有效咖啡记录数：$validCoffeeRecords")

// 显示数据示例
println("\n咖啡数据示例（前3条）：")
coffeeRDD.take(3).foreach { data =>
  println(f"${data.state} | ${data.product} | ${data.coffeeType} | 销售额:${data.coffeeSales.formatted("%.2f")} | 利润:${data.profit.formatted("%.2f")}")
}

// 2. 产品销售排名分析
println("\n=== 产品销售排名分析 ===")

val productSalesRanking = coffeeRDD
  .map(data => (data.product, data.coffeeSales))
  .reduceByKey(_ + _)
  .sortBy(_._2, ascending = false)
  .take(15)

println("产品销售额排名（Top 15）：")
println("排名\t产品名称\t\t\t销售额")
productSalesRanking.zipWithIndex.foreach { case ((product, sales), index) =>
  println(f"${index + 1}%2d\t$product%-25s\t${sales.formatted("%.2f")}")
}

// 3. 各州销售分析
println("\n=== 各州销售分析 ===")

val stateSalesAnalysis = coffeeRDD
  .map(data => (data.state, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (state, (totalSales, totalProfit, count)) =>
    val avgSales = totalSales / count
    val avgProfit = totalProfit / count
    val profitMargin = if (totalSales > 0) totalProfit / totalSales * 100 else 0
    (state, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("各州销售统计：")
println("州名\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
stateSalesAnalysis.foreach { case (state, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count) =>
  println(f"$state%-12s\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t$count")
}

// 4. 市场分析
println("\n=== 市场分析 ===")

val marketAnalysis = coffeeRDD
  .map(data => (data.market, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (market, (totalSales, totalProfit, count)) =>
    val avgSales = totalSales / count
    val avgProfit = totalProfit / count
    val profitMargin = if (totalSales > 0) totalProfit / totalSales * 100 else 0
    (market, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("各市场销售统计：")
println("市场\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
marketAnalysis.foreach { case (market, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count) =>
  println(f"$market%-12s\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t$count")
}

// 5. 整体统计分析
println("\n=== 整体统计分析 ===")

val allCoffeeData = coffeeRDD.collect()
val totalSales = allCoffeeData.map(_.coffeeSales).sum
val totalProfit = allCoffeeData.map(_.profit).sum
val totalCogs = allCoffeeData.map(_.cogs).sum
val totalMarketing = allCoffeeData.map(_.marketing).sum

val avgSales = totalSales / allCoffeeData.length
val avgProfit = totalProfit / allCoffeeData.length
val avgCogs = totalCogs / allCoffeeData.length
val avgMarketing = totalMarketing / allCoffeeData.length

val overallProfitMargin = (totalProfit / totalSales * 100).formatted("%.2f")

println("整体业务指标：")
println(f"总销售额：${totalSales.formatted("%.2f")}")
println(f"总利润：${totalProfit.formatted("%.2f")}")
println(f"总成本：${totalCogs.formatted("%.2f")}")
println(f"总营销费用：${totalMarketing.formatted("%.2f")}")
println(f"平均销售额：${avgSales.formatted("%.2f")}")
println(f"平均利润：${avgProfit.formatted("%.2f")}")
println(f"平均成本：${avgCogs.formatted("%.2f")}")
println(f"平均营销费用：${avgMarketing.formatted("%.2f")}")
println(f"整体利润率：$overallProfitMargin%")

// 6. 咖啡类型分析
println("\n=== 咖啡类型分析 ===")

val coffeeTypeAnalysis = coffeeRDD
  .map(data => (data.coffeeType, (data.coffeeSales, data.profit, data.margin, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4))
  .map { case (coffeeType, (sales, profit, margin, count)) =>
    val avgSales = sales / count
    val avgProfit = profit / count
    val avgMargin = margin / count
    val profitMargin = if (sales > 0) profit / sales * 100 else 0
    (coffeeType, sales, profit, avgSales, avgProfit, avgMargin, profitMargin, count)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("咖啡类型分析：")
println("类型\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t平均利润率\t利润率\t\t记录数")
coffeeTypeAnalysis.foreach { case (coffeeType, sales, profit, avgSales, avgProfit, avgMargin, profitMargin, count) =>
  println(f"$coffeeType%-8s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${avgMargin.formatted("%.2f")}\t\t${profitMargin.formatted("%.2f")}%\t\t$count")
}

// 7. 产品类型分析
println("\n=== 产品类型分析 ===")

val productTypeAnalysis = coffeeRDD
  .map(data => (data.productType, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (productType, (sales, profit, count)) =>
    val avgSales = sales / count
    val avgProfit = profit / count
    val profitMargin = if (sales > 0) profit / sales * 100 else 0
    (productType, sales, profit, avgSales, avgProfit, profitMargin, count)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("产品类型分析：")
println("产品类型\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
productTypeAnalysis.foreach { case (productType, sales, profit, avgSales, avgProfit, profitMargin, count) =>
  println(f"$productType%-15s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t$count")
}

// 8. 保存咖啡分析结果
println("\n正在保存咖啡分析结果...")

val coffeeResults = sc.parallelize(Seq(
  "=" * 80,
  "咖啡连锁店数据分析报告",
  "=" * 80,
  s"分析时间：${java.time.LocalDateTime.now()}",
  s"数据文件：/home/<USER>/CoffeeChain.csv",
  s"分析记录数：$validCoffeeRecords",
  "",
  "核心业务指标：",
  f"总销售额：${totalSales.formatted("%.2f")}",
  f"总利润：${totalProfit.formatted("%.2f")}",
  f"平均销售额：${avgSales.formatted("%.2f")}",
  f"平均利润：${avgProfit.formatted("%.2f")}",
  f"整体利润率：$overallProfitMargin%",
  "",
  "产品销售排名（Top 10）：",
  productSalesRanking.take(10).zipWithIndex.map { case ((product, sales), index) =>
    f"${index + 1}. $product: ${sales.formatted("%.2f")}"
  }.mkString("\n"),
  "",
  "各州销售统计：",
  "州名\t\t总销售额\t总利润\t\t利润率",
  stateSalesAnalysis.map { case (state, totalSales, totalProfit, _, _, profitMargin, _) =>
    f"$state\t\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%"
  }.mkString("\n"),
  "",
  "各市场销售统计：",
  "市场\t\t总销售额\t总利润\t\t利润率",
  marketAnalysis.map { case (market, totalSales, totalProfit, _, _, profitMargin, _) =>
    f"$market\t\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%"
  }.mkString("\n"),
  "",
  "咖啡类型分析：",
  "类型\t\t总销售额\t总利润\t\t利润率",
  coffeeTypeAnalysis.map { case (coffeeType, sales, profit, _, _, _, profitMargin, _) =>
    f"$coffeeType\t\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%"
  }.mkString("\n")
))

coffeeResults.coalesce(1).saveAsTextFile("/home/<USER>/coffee_analysis_results")
println("✓ 咖啡分析结果已保存到：/home/<USER>/coffee_analysis_results")

println("\n=== 第二部分完成 ===")
println("\n=== 所有分析完成 ===")

// 显示结果文件
println("\n生成的结果文件：")
println("- /home/<USER>/population_analysis_results/part-00000    # 人口分析结果")
println("- /home/<USER>/coffee_analysis_results/part-00000       # 咖啡分析结果")

println("\n可以使用以下命令查看结果：")
println("sc.textFile(\"/home/<USER>/population_analysis_results/part-00000\").collect().foreach(println)")
println("sc.textFile(\"/home/<USER>/coffee_analysis_results/part-00000\").collect().foreach(println)")

println("\n=== Spark Shell 分析完成 ===")
