// Spark Shell 交互式分析代码
// 在spark-shell中逐段复制粘贴运行
// 工作目录：/home/<USER>

println("=" * 80)
println("Spark Shell 交互式数据分析系统")
println("工作目录：/home/<USER>/spark/")
println("=" * 80)

// ===== 第一部分：人口数据生成和分析 =====
println("\n=== 第一部分：人口数据生成和分析 ===")

// 1. 生成人口数据
import scala.util.Random
import java.io.{PrintWriter, File}

def generatePopulationData(): Unit = {
  val recordCount = 1000
  val minAge = 18
  val maxAge = 90
  val outputPath = "/home/<USER>/spark/peopleage.txt"

  println(s"正在生成 $recordCount 条人口年龄数据...")
  println("数据格式：序号    年龄")

  val random = new Random()
  val writer = new PrintWriter(new File(outputPath))

  try {
    for (i <- 1 to recordCount) {
      val age = minAge + random.nextInt(maxAge - minAge + 1)
      writer.println(s"$i\t$age")  // 使用制表符分隔

      if (i % 200 == 0) {
        println(s"已生成 $i 条记录...")
      }
    }

    println(s"✓ 人口数据生成完成：$outputPath")

  } finally {
    writer.close()
  }
}

// 执行数据生成
generatePopulationData()

// 2. 读取和分析人口数据
val populationRawData = sc.textFile("/home/<USER>/spark/peopleage.txt")
val totalRecords = populationRawData.count()

println(s"\n人口数据读取完成，记录数：$totalRecords")

// 显示数据示例
println("\n人口数据示例（前5条）：")
populationRawData.take(5).foreach(println)

// 解析数据：提取年龄字段
val agesRDD = populationRawData.map { line =>
  val parts = line.split("\t")
  parts(1).toInt  // 提取年龄（第二列）
}

// 缓存RDD以提高性能
agesRDD.cache()

val validAgeRecords = agesRDD.count()
println(s"有效年龄记录数：$validAgeRecords")

// 3. 基础年龄统计
println("\n=== 基础年龄统计分析 ===")

val totalAge = agesRDD.reduce(_ + _)
val ageCount = agesRDD.count()
val averageAge = totalAge.toDouble / ageCount
val maxAge = agesRDD.max()
val minAge = agesRDD.min()

// 计算中位数
val sortedAges = agesRDD.collect().sorted
val median = if (sortedAges.length % 2 == 0) {
  (sortedAges(sortedAges.length / 2 - 1) + sortedAges(sortedAges.length / 2)) / 2.0
} else {
  sortedAges(sortedAges.length / 2).toDouble
}

// 计算方差和标准差
val mean = averageAge
val variance = agesRDD.map(age => math.pow(age - mean, 2)).mean()
val stdDev = math.sqrt(variance)

println(f"总人数：$ageCount")
println(f"年龄总和：$totalAge")
println(f"平均年龄：$averageAge%.2f 岁")
println(f"年龄中位数：$median%.1f 岁")
println(f"最大年龄：$maxAge 岁")
println(f"最小年龄：$minAge 岁")
println(f"年龄跨度：${maxAge - minAge} 岁")
println(f"标准差：$stdDev%.2f 岁")
println(f"方差：$variance%.2f")

// 4. 年龄段分布分析
println("\n=== 年龄段分布分析 ===")

val ageGroups = agesRDD.map { age =>
  val ageGroup = age match {
    case a if a < 18 => "未成年(0-17岁)"
    case a if a < 25 => "青年初期(18-24岁)"
    case a if a < 35 => "青年期(25-34岁)"
    case a if a < 45 => "中年初期(35-44岁)"
    case a if a < 55 => "中年期(45-54岁)"
    case a if a < 65 => "中年后期(55-64岁)"
    case _ => "老年期(65岁以上)"
  }
  (ageGroup, age)
}

val ageGroupStats = ageGroups
  .groupByKey()
  .map { case (group, ages) =>
    val ageList = ages.toList
    val count = ageList.length
    val avgAge = ageList.sum.toDouble / count
    (group, count, avgAge)
  }
  .collect()
  .sortBy(_._2)(Ordering[Int].reverse)

println("年龄段分布统计：")
println("年龄段\t\t\t人数\t比例\t\t平均年龄")
ageGroupStats.foreach { case (group, count, avgAge) =>
  val percentage = (count.toDouble / ageCount * 100).formatted("%.2f")
  println(f"$group%-15s\t$count\t$percentage%%-8s\t$avgAge%.2f岁")
}

// 5. 保存人口分析结果
println("\n正在保存人口分析结果...")

val populationResults = sc.parallelize(Seq(
  "=" * 60,
  "人口年龄数据分析报告",
  "=" * 60,
  s"分析时间：${java.time.LocalDateTime.now()}",
  s"数据文件：/home/<USER>/spark/peopleage.txt",
  s"总记录数：$ageCount",
  "",
  "基础统计：",
  s"年龄总和：$totalAge",
  f"平均年龄：$averageAge%.2f 岁",
  f"年龄中位数：$median%.1f 岁",
  s"最大年龄：$maxAge 岁",
  s"最小年龄：$minAge 岁",
  s"年龄跨度：${maxAge - minAge} 岁",
  f"标准差：$stdDev%.2f 岁",
  f"方差：$variance%.2f",
  "",
  "年龄段分布：",
  ageGroupStats.map { case (group, count, avgAge) =>
    val percentage = (count.toDouble / ageCount * 100).formatted("%.2f")
    f"$group: $count 人 ($percentage%), 平均年龄 $avgAge%.2f岁"
  }.mkString("\n")
))

populationResults.coalesce(1).saveAsTextFile("/home/<USER>/spark/population_analysis_result")
println("✓ 人口分析结果已保存到：/home/<USER>/spark/population_analysis_result")

println("\n=== 第一部分完成 ===")

// ===== 第二部分：咖啡数据分析 =====
println("\n=== 第二部分：咖啡数据分析 ===")

// 1. 读取咖啡数据
val coffeeRawData = sc.textFile("/home/<USER>/spark/CoffeeChain.csv")
val coffeeHeader = coffeeRawData.first()
val coffeeDataLines = coffeeRawData.filter(_ != coffeeHeader)

println(s"咖啡数据读取完成，记录数：${coffeeDataLines.count()}")
println("数据表头：")
println(coffeeHeader)

// 定义简化的咖啡数据结构
case class CoffeeRecord(
  state: String,
  market: String,
  product: String,
  productType: String,
  coffeeType: String,
  coffeeSales: Double,
  profit: Double,
  margin: Double,
  cogs: Double,
  marketing: Double
)

def parseCoffeeRecord(line: String): Option[CoffeeRecord] = {
  try {
    val parts = line.split(",")
    if (parts.length >= 20) {
      Some(CoffeeRecord(
        state = parts(6),
        market = parts(2),
        product = parts(4),
        productType = parts(5),
        coffeeType = parts(7),
        coffeeSales = parts(12).replaceAll("\"", "").replaceAll(",", "").toDouble,
        profit = parts(18).toDouble,
        margin = parts(15).toDouble,
        cogs = parts(13).toDouble,
        marketing = parts(16).toDouble
      ))
    } else None
  } catch {
    case _: Exception => None
  }
}

val coffeeRDD = coffeeDataLines.map(parseCoffeeRecord).filter(_.isDefined).map(_.get)
coffeeRDD.cache()

val validCoffeeRecords = coffeeRDD.count()
println(s"有效咖啡记录数：$validCoffeeRecords")

// 显示数据示例
println("\n咖啡数据示例（前3条）：")
coffeeRDD.take(3).foreach { data =>
  println(f"${data.state} | ${data.product} | ${data.coffeeType} | 销售额:${data.coffeeSales.formatted("%.2f")} | 利润:${data.profit.formatted("%.2f")}")
}

// 2. 产品销售排名分析
println("\n=== 产品销售排名分析 ===")

val productSalesRanking = coffeeRDD
  .map(data => (data.product, data.coffeeSales))
  .reduceByKey(_ + _)
  .sortBy(_._2, ascending = false)
  .take(15)

println("产品销售额排名（Top 15）：")
println("排名\t产品名称\t\t\t销售额")
productSalesRanking.zipWithIndex.foreach { case ((product, sales), index) =>
  println(f"${index + 1}%2d\t$product%-25s\t${sales.formatted("%.2f")}")
}

// 3. 各州销售分析
println("\n=== 各州销售分析 ===")

val stateSalesAnalysis = coffeeRDD
  .map(data => (data.state, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (state, (totalSales, totalProfit, count)) =>
    val avgSales = totalSales / count
    val avgProfit = totalProfit / count
    val profitMargin = if (totalSales > 0) totalProfit / totalSales * 100 else 0
    (state, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("各州销售统计：")
println("州名\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
stateSalesAnalysis.foreach { case (state, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count) =>
  println(f"$state%-12s\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t$count")
}

// 4. 市场分析
println("\n=== 市场分析 ===")

val marketAnalysis = coffeeRDD
  .map(data => (data.market, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (market, (totalSales, totalProfit, count)) =>
    val avgSales = totalSales / count
    val avgProfit = totalProfit / count
    val profitMargin = if (totalSales > 0) totalProfit / totalSales * 100 else 0
    (market, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("各市场销售统计：")
println("市场\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
marketAnalysis.foreach { case (market, totalSales, totalProfit, avgSales, avgProfit, profitMargin, count) =>
  println(f"$market%-12s\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t$count")
}

// 5. 整体统计分析
println("\n=== 整体统计分析 ===")

val allCoffeeData = coffeeRDD.collect()
val totalSales = allCoffeeData.map(_.coffeeSales).sum
val totalProfit = allCoffeeData.map(_.profit).sum
val totalCogs = allCoffeeData.map(_.cogs).sum
val totalMarketing = allCoffeeData.map(_.marketing).sum

val avgSales = totalSales / allCoffeeData.length
val avgProfit = totalProfit / allCoffeeData.length
val avgCogs = totalCogs / allCoffeeData.length
val avgMarketing = totalMarketing / allCoffeeData.length

val overallProfitMargin = (totalProfit / totalSales * 100).formatted("%.2f")

println("整体业务指标：")
println(f"总销售额：${totalSales.formatted("%.2f")}")
println(f"总利润：${totalProfit.formatted("%.2f")}")
println(f"总成本：${totalCogs.formatted("%.2f")}")
println(f"总营销费用：${totalMarketing.formatted("%.2f")}")
println(f"平均销售额：${avgSales.formatted("%.2f")}")
println(f"平均利润：${avgProfit.formatted("%.2f")}")
println(f"平均成本：${avgCogs.formatted("%.2f")}")
println(f"平均营销费用：${avgMarketing.formatted("%.2f")}")
println(f"整体利润率：$overallProfitMargin%")

// 6. 咖啡类型分析
println("\n=== 咖啡类型分析 ===")

val coffeeTypeAnalysis = coffeeRDD
  .map(data => (data.coffeeType, (data.coffeeSales, data.profit, data.margin, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4))
  .map { case (coffeeType, (sales, profit, margin, count)) =>
    val avgSales = sales / count
    val avgProfit = profit / count
    val avgMargin = margin / count
    val profitMargin = if (sales > 0) profit / sales * 100 else 0
    (coffeeType, sales, profit, avgSales, avgProfit, avgMargin, profitMargin, count)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("咖啡类型分析：")
println("类型\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t平均利润率\t利润率\t\t记录数")
coffeeTypeAnalysis.foreach { case (coffeeType, sales, profit, avgSales, avgProfit, avgMargin, profitMargin, count) =>
  println(f"$coffeeType%-8s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${avgMargin.formatted("%.2f")}\t\t${profitMargin.formatted("%.2f")}%\t\t$count")
}

// 7. 产品类型分析
println("\n=== 产品类型分析 ===")

val productTypeAnalysis = coffeeRDD
  .map(data => (data.productType, (data.coffeeSales, data.profit, 1)))
  .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
  .map { case (productType, (sales, profit, count)) =>
    val avgSales = sales / count
    val avgProfit = profit / count
    val profitMargin = if (sales > 0) profit / sales * 100 else 0
    (productType, sales, profit, avgSales, avgProfit, profitMargin, count)
  }
  .sortBy(_._2, ascending = false)
  .collect()

println("产品类型分析：")
println("产品类型\t\t总销售额\t总利润\t\t平均销售额\t平均利润\t利润率\t\t记录数")
productTypeAnalysis.foreach { case (productType, sales, profit, avgSales, avgProfit, profitMargin, count) =>
  println(f"$productType%-15s\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${avgSales.formatted("%.2f")}\t\t${avgProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%\t\t$count")
}

// 8. 保存咖啡分析结果
println("\n正在保存咖啡分析结果...")

val coffeeResults = sc.parallelize(Seq(
  "=" * 80,
  "咖啡连锁店数据分析报告",
  "=" * 80,
  s"分析时间：${java.time.LocalDateTime.now()}",
  s"数据文件：/home/<USER>/spark/CoffeeChain.csv",
  s"分析记录数：$validCoffeeRecords",
  "",
  "核心业务指标：",
  f"总销售额：${totalSales.formatted("%.2f")}",
  f"总利润：${totalProfit.formatted("%.2f")}",
  f"平均销售额：${avgSales.formatted("%.2f")}",
  f"平均利润：${avgProfit.formatted("%.2f")}",
  f"整体利润率：$overallProfitMargin%",
  "",
  "产品销售排名（Top 10）：",
  productSalesRanking.take(10).zipWithIndex.map { case ((product, sales), index) =>
    f"${index + 1}. $product: ${sales.formatted("%.2f")}"
  }.mkString("\n"),
  "",
  "各州销售统计：",
  "州名\t\t总销售额\t总利润\t\t利润率",
  stateSalesAnalysis.map { case (state, totalSales, totalProfit, _, _, profitMargin, _) =>
    f"$state\t\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%"
  }.mkString("\n"),
  "",
  "各市场销售统计：",
  "市场\t\t总销售额\t总利润\t\t利润率",
  marketAnalysis.map { case (market, totalSales, totalProfit, _, _, profitMargin, _) =>
    f"$market\t\t${totalSales.formatted("%.2f")}\t${totalProfit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%"
  }.mkString("\n"),
  "",
  "咖啡类型分析：",
  "类型\t\t总销售额\t总利润\t\t利润率",
  coffeeTypeAnalysis.map { case (coffeeType, sales, profit, _, _, _, profitMargin, _) =>
    f"$coffeeType\t\t${sales.formatted("%.2f")}\t${profit.formatted("%.2f")}\t${profitMargin.formatted("%.2f")}%"
  }.mkString("\n")
))

coffeeResults.coalesce(1).saveAsTextFile("/home/<USER>/spark/coffee_comprehensive_analysis")
println("✓ 咖啡分析结果已保存到：/home/<USER>/spark/coffee_comprehensive_analysis")

println("\n=== 第二部分完成 ===")
println("\n=== 所有分析完成 ===")

// 显示结果文件
println("\n生成的结果文件：")
println("- /home/<USER>/spark/population_analysis_result/part-00000    # 人口分析结果")
println("- /home/<USER>/spark/coffee_comprehensive_analysis/part-00000       # 咖啡分析结果")

println("\n可以使用以下命令查看结果：")
println("sc.textFile(\"/home/<USER>/spark/population_analysis_result/part-00000\").collect().foreach(println)")
println("sc.textFile(\"/home/<USER>/spark/coffee_comprehensive_analysis/part-00000\").collect().foreach(println)")

println("\n=== Spark Shell 分析完成 ===")
