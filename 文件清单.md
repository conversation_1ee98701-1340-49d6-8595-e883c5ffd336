# 项目文件清单

## 核心文件

### 1. 数据文件
- **CoffeeChain.csv** - 咖啡连锁店销售数据（原始数据文件）
- **peopleage.txt** - 生成的人口年龄数据（运行后产生）

### 2. 程序文件
- **GeneratePeopleAge.scala** - 生成模拟人口年龄数据的程序
- **CalculateAverageAge.scala** - 计算人口平均年龄的Spark程序
- **CoffeeChainAnalysis.scala** - 咖啡连锁店数据分析的完整程序
- **spark_commands.scala** - 在spark-shell中运行的简化版本

### 3. 脚本文件
- **run_analysis.sh** - 自动化运行脚本（检查环境并执行所有分析）

### 4. 文档文件
- **实训报告.md** - 完整的实训报告（包含代码、结果、分析）
- **任务要求.md** - 实训任务要求说明
- **运行指南.md** - 详细的运行指导说明
- **文件清单.md** - 本文件，项目文件说明

## 输出文件（运行后生成）

### 1. 人口年龄分析结果
- **people_age_analysis/** - spark-shell版本的分析结果目录
  - `part-00000` - 具体结果文件
- **average_age_result.txt/** - 编译版本的分析结果目录
  - `part-00000` - 具体结果文件

### 2. 咖啡数据分析结果
- **coffee_analysis_results/** - spark-shell版本的分析结果目录
  - `part-00000` - 具体结果文件
- **coffee_sales_ranking/** - 编译版本的销售排名结果目录
  - `part-00000` - 具体结果文件
- **coffee_distribution_analysis/** - 编译版本的分布分析结果目录
  - `part-00000` - 具体结果文件

## 文件用途说明

### 程序文件详解

#### GeneratePeopleAge.scala
- **功能**：生成1000条模拟人口年龄数据
- **输入**：无
- **输出**：peopleage.txt文件
- **运行方式**：`scala GeneratePeopleAge.scala`

#### CalculateAverageAge.scala
- **功能**：使用Spark RDD计算人口平均年龄
- **输入**：peopleage.txt
- **输出**：average_age_result.txt/目录
- **运行方式**：`spark-submit --class CalculateAverageAge --master local[*] .`

#### CoffeeChainAnalysis.scala
- **功能**：完整的咖啡连锁店数据分析
- **输入**：CoffeeChain.csv
- **输出**：coffee_sales_ranking/和coffee_distribution_analysis/目录
- **运行方式**：`spark-submit --class CoffeeChainAnalysis --master local[*] .`

#### spark_commands.scala
- **功能**：在spark-shell中运行的简化版本
- **输入**：CoffeeChain.csv（需要先生成peopleage.txt）
- **输出**：people_age_analysis/和coffee_analysis_results/目录
- **运行方式**：在spark-shell中复制粘贴代码

### 脚本文件详解

#### run_analysis.sh
- **功能**：自动化运行所有分析程序
- **检查项**：Java、Scala、Spark环境，数据文件
- **执行内容**：
  1. 环境检查
  2. 生成人口数据
  3. 计算平均年龄
  4. 分析咖啡数据
  5. 显示结果摘要

### 文档文件详解

#### 实训报告.md
- **内容**：完整的实训报告
- **包含**：
  - 实验背景与目标
  - 实验环境与工具
  - 详细的代码实现
  - 实验结果与分析
  - 问题与解决方案
  - 结论与总结

#### 运行指南.md
- **内容**：详细的运行指导
- **包含**：
  - 环境准备步骤
  - 三种运行方式
  - 结果查看方法
  - 常见问题解决
  - 截图建议

## 推荐运行顺序

### 方式一：使用spark-shell（推荐新手）
1. 检查环境：Java、Scala、Spark
2. 启动：`spark-shell`
3. 运行：复制粘贴`spark_commands.scala`中的代码
4. 查看结果：使用scala命令查看生成的文件

### 方式二：编译运行（推荐有经验者）
1. 生成数据：`scala GeneratePeopleAge.scala`
2. 编译程序：`scalac -cp "$SPARK_HOME/jars/*" *.scala`
3. 运行分析：`spark-submit --class ClassName --master local[*] .`
4. 查看结果：`cat result_directory/part-00000`

### 方式三：自动化脚本（推荐批量运行）
1. 设置权限：`chmod +x run_analysis.sh`
2. 运行脚本：`./run_analysis.sh`
3. 查看输出：脚本会自动显示结果摘要

## 注意事项

1. **文件依赖关系**：
   - CalculateAverageAge.scala 依赖 peopleage.txt
   - CoffeeChainAnalysis.scala 依赖 CoffeeChain.csv
   - spark_commands.scala 包含数据生成和分析的完整流程

2. **输出文件冲突**：
   - 不同运行方式会生成不同名称的输出目录
   - 重复运行前请删除或重命名已有的输出目录

3. **内存要求**：
   - 建议虚拟机至少分配2GB内存
   - 可以通过Spark参数调整内存使用

4. **权限要求**：
   - 确保对当前目录有读写权限
   - 脚本文件需要执行权限

## 故障排除

如果遇到问题，请按以下顺序检查：

1. **环境问题**：检查Java、Scala、Spark版本
2. **文件问题**：确认数据文件存在且格式正确
3. **权限问题**：检查文件和目录权限
4. **内存问题**：调整Spark内存参数
5. **依赖问题**：确认所有依赖文件都在正确位置

详细的故障排除方法请参考`运行指南.md`文件。
