import org.apache.spark.SparkContext
import org.apache.spark.SparkConf

/**
 * 使用Spark RDD计算人口平均年龄
 * 读取peopleage.txt文件，计算所有人口的平均年龄
 */
object CalculateAverageAge {
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("Calculate Average Age")
      .setMaster("local[*]")  // 本地模式，使用所有可用核心
    
    // 创建SparkContext
    val sc = new SparkContext(conf)
    
    try {
      println("=== Spark RDD 人口平均年龄计算 ===")
      
      // 读取数据文件
      val inputFile = "/home/<USER>/me/peopleage.txt"
      println(s"正在读取数据文件：$inputFile")
      
      // 创建RDD并读取数据
      val linesRDD = sc.textFile(inputFile)
      
      // 显示总记录数
      val totalRecords = linesRDD.count()
      println(s"总记录数：$totalRecords")
      
      // 显示前5条原始数据
      println("\n原始数据示例（前5行）：")
      linesRDD.take(5).foreach(println)
      
      // 数据转换：提取年龄字段
      // 每行格式：序号\t年龄
      val agesRDD = linesRDD.map { line =>
        val parts = line.split("\t")
        if (parts.length >= 2) {
          parts(1).toInt  // 提取年龄（第二列）
        } else {
          throw new IllegalArgumentException(s"数据格式错误：$line")
        }
      }
      
      // 缓存RDD以提高性能
      agesRDD.cache()
      
      // 计算统计信息
      println("\n=== 年龄统计分析 ===")
      
      // 1. 计算平均年龄
      val totalAge = agesRDD.reduce(_ + _)  // 求和
      val count = agesRDD.count()           // 计数
      val averageAge = totalAge.toDouble / count
      
      println(s"年龄总和：$totalAge")
      println(s"人口总数：$count")
      println(s"平均年龄：${averageAge.formatted("%.2f")} 岁")
      
      // 2. 计算最大年龄和最小年龄
      val maxAge = agesRDD.max()
      val minAge = agesRDD.min()
      
      println(s"最大年龄：$maxAge 岁")
      println(s"最小年龄：$minAge 岁")
      println(s"年龄范围：$minAge - $maxAge 岁")
      
      // 3. 年龄分布统计
      println("\n=== 年龄分布统计 ===")
      
      // 按年龄段分组统计
      val ageGroups = agesRDD.map { age =>
        age match {
          case a if a < 30 => "18-29岁"
          case a if a < 40 => "30-39岁"
          case a if a < 50 => "40-49岁"
          case a if a < 60 => "50-59岁"
          case a if a < 70 => "60-69岁"
          case _ => "70岁以上"
        }
      }
      
      val ageGroupCounts = ageGroups.countByValue()
      
      println("各年龄段人数分布：")
      ageGroupCounts.toSeq.sortBy(_._1).foreach { case (group, count) =>
        val percentage = (count.toDouble / totalRecords * 100).formatted("%.1f")
        println(s"$group: $count 人 ($percentage%)")
      }
      
      // 4. 保存结果到文件
      val resultFile = "/home/<USER>/me/average_age_result.txt"
      val results = sc.parallelize(Seq(
        "=== 人口年龄统计结果 ===",
        s"数据文件：$inputFile",
        s"总记录数：$totalRecords",
        s"平均年龄：${averageAge.formatted("%.2f")} 岁",
        s"最大年龄：$maxAge 岁",
        s"最小年龄：$minAge 岁",
        s"年龄范围：$minAge - $maxAge 岁",
        "",
        "年龄分布统计：",
        ageGroupCounts.toSeq.sortBy(_._1).map { case (group, count) =>
          val percentage = (count.toDouble / totalRecords * 100).formatted("%.1f")
          s"$group: $count 人 ($percentage%)"
        }.mkString("\n")
      ))
      
      results.coalesce(1).saveAsTextFile(resultFile)
      println(s"\n结果已保存到文件：$resultFile")
      
      println("\n=== 计算完成 ===")
      
    } catch {
      case e: Exception =>
        println(s"程序执行出错：${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 关闭SparkContext
      sc.stop()
    }
  }
}
