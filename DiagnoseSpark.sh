#!/bin/bash

# Spark环境诊断脚本
# 用于检查Spark环境和分析结果生成问题

echo "=" * 60
echo "Spark环境诊断脚本"
echo "=" * 60

WORK_DIR="/home/<USER>/spark"
cd $WORK_DIR

echo "1. 检查工作目录..."
echo "当前目录：$(pwd)"
echo "目录权限：$(ls -ld $WORK_DIR)"
echo ""

echo "2. 检查Java环境..."
if command -v java &> /dev/null; then
    java -version
    echo "✓ Java环境正常"
else
    echo "✗ Java环境未找到"
fi
echo ""

echo "3. 检查Scala环境..."
if command -v scala &> /dev/null; then
    scala -version
    echo "✓ Scala环境正常"
else
    echo "✗ Scala环境未找到"
fi
echo ""

echo "4. 检查Spark环境..."
if command -v spark-submit &> /dev/null; then
    spark-submit --version
    echo "✓ Spark环境正常"
else
    echo "✗ Spark环境未找到"
fi
echo ""

echo "5. 检查必要文件..."
FILES=("DataGenerator.scala" "PopulationAnalyzer.scala" "CoffeeDataAnalyzer.scala" "CoffeeChain.csv")
for file in "${FILES[@]}"; do
    if [ -f "$WORK_DIR/$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 不存在"
    fi
done
echo ""

echo "6. 检查现有结果目录..."
RESULT_DIRS=("population_analysis_result" "coffee_comprehensive_analysis")
for dir in "${RESULT_DIRS[@]}"; do
    if [ -d "$WORK_DIR/$dir" ]; then
        echo "✓ $dir 目录存在"
        echo "  文件列表："
        ls -la "$WORK_DIR/$dir"
    else
        echo "✗ $dir 目录不存在"
    fi
done
echo ""

echo "7. 运行Spark测试..."
if [ -f "$WORK_DIR/TestSpark.scala" ]; then
    echo "正在运行Spark测试程序..."
    scala TestSpark.scala
else
    echo "✗ TestSpark.scala 不存在，跳过测试"
fi
echo ""

echo "8. 检查磁盘空间..."
df -h $WORK_DIR
echo ""

echo "9. 检查内存使用..."
free -h
echo ""

echo "10. 生成简单的人口数据进行测试..."
if scala DataGenerator.scala; then
    echo "✓ 数据生成成功"
    if [ -f "$WORK_DIR/peopleage.txt" ]; then
        echo "✓ peopleage.txt 文件已生成"
        echo "文件大小：$(du -h $WORK_DIR/peopleage.txt)"
        echo "前5行内容："
        head -5 "$WORK_DIR/peopleage.txt"
    else
        echo "✗ peopleage.txt 文件未生成"
    fi
else
    echo "✗ 数据生成失败"
fi
echo ""

echo "11. 测试人口数据分析..."
if spark-submit --class PopulationAnalyzer --master local[*] PopulationAnalyzer.scala; then
    echo "✓ 人口数据分析完成"
    if [ -d "$WORK_DIR/population_analysis_result" ]; then
        echo "✓ 分析结果目录已生成"
        ls -la "$WORK_DIR/population_analysis_result"
        if [ -f "$WORK_DIR/population_analysis_result/part-00000" ]; then
            echo "✓ 结果文件存在"
            echo "文件内容："
            cat "$WORK_DIR/population_analysis_result/part-00000"
        else
            echo "✗ 结果文件不存在"
        fi
    else
        echo "✗ 分析结果目录未生成"
    fi
else
    echo "✗ 人口数据分析失败"
fi
echo ""

echo "=" * 60
echo "诊断完成"
echo "=" * 60
